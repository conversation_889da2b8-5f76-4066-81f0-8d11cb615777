package com.yhl.scp.mps.domain.complatecheck.process;


import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.mps.domain.complatecheck.ICompleteCheck;
import com.yhl.scp.mps.domain.complatecheck.model.context.CompleteDataContext;
import com.yhl.scp.mps.domain.complatecheck.support.BaseCompleteSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
public abstract class AbstractCompleteCheck extends BaseCompleteSupport implements ICompleteCheck {


    @Override
    @Transactional
    public void doCompleteCheck() {
        try {
            // 0.计划调整
            planAdjust();
            // 1.构建流程数据传输实体
            CompleteDataContext completeDataContext = initContext();
            // 2.分配前处理，还原所有分配关系
            completeUnfolding(completeDataContext);
            // 3.执行齐套检查
            completeCheck(completeDataContext);
            // 4.数据库操作
            databaseOperation(completeDataContext);
        } catch (Exception e) {
            log.error("齐套检查执行失败", e);
            cacheLog("齐套检查执行失败：" + e.getMessage());
            throw new BusinessException("齐套检查执行失败：" + e.getMessage());
        }
    }


    /**
     * 计划调整
     */
    protected abstract void planAdjust();

    /**
     * 分配前处理
     */
    protected abstract void completeUnfolding(CompleteDataContext completeDataContext);

    /**
     * 执行物料分配（子流程AbstractCheckProcess应用实现）
     *
     * @param completeDataContext model
     */
    protected abstract void completeCheck(CompleteDataContext completeDataContext);

    /**
     * 数据库操作
     *
     * @param completeDataContext model
     */
    protected abstract void databaseOperation(CompleteDataContext completeDataContext);


}
