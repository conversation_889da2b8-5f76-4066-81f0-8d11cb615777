package com.yhl.scp.mps.domain.dispatch.process;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.mds.enums.StandardStepEnum;
import com.yhl.scp.mds.extension.rule.vo.RuleEncodingsVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mps.dispatch.output.RzzMpsAlgorithmOutput;
import com.yhl.scp.mps.domain.dispatch.model.context.MpsAnalysisContext;
import com.yhl.scp.mps.domain.dispatch.model.dto.SplitResourceDTO;
import com.yhl.scp.mps.domain.dispatch.model.dto.SplitRoutingStepResourceDTO;
import com.yhl.scp.mps.domain.dispatch.model.dto.SplitStepResourceResultDTO;
import com.yhl.scp.mps.manualAdjust.dao.ManualAdjustHandleDao;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanExtDao;
import com.yhl.scp.mps.product.service.ProductAdvanceBatchRuleService;
import com.yhl.scp.mps.product.vo.ProductAdvanceBatchRuleVO;
import com.yhl.scp.sds.basic.order.infrastructure.po.WorkOrderBasicPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>AbstractSplitOrder</code>
 *
 * <p>大单拆小单流程定义实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-27 22:37:28
 */
@Slf4j
public abstract class AbstractSplitOrder extends AbstractMpsResult {

    public static final Integer SINGLE_HOUR = 4;
    @Resource
    private ProductAdvanceBatchRuleService productAdvanceBatchRuleService;
    @Resource
    private MasterPlanExtDao masterPlanExtDao;
    @Resource
    private ManualAdjustHandleDao manualAdjustHandleDao;

    /**
     * 组合大单拆分，尝试插入小单
     *
     * @param routingYieldResourceMap
     * @param ruleEncodingsMap
     * @param combinationBigWorkOrders
     * @param combinationSmallWorkOrders
     * @return
     */
    private Map<WorkOrderPO, List<WorkOrderPO>> getSplitWorkOrders(
            String physicalResourceId,
            Map<String, SplitStepResourceResultDTO> routingYieldResourceMap,
            Map<String, RuleEncodingsVO> ruleEncodingsMap,
            List<List<WorkOrderPO>> combinationBigWorkOrders,
            List<List<WorkOrderPO>> combinationSmallWorkOrders) {
        Map<WorkOrderPO, List<WorkOrderPO>> result = MapUtil.newHashMap();
        if (CollectionUtils.isEmpty(combinationSmallWorkOrders)) {
            return result;
        }
        for (int i = 0; i < combinationBigWorkOrders.size(); i++) {
            List<WorkOrderPO> workOrderPOList = combinationBigWorkOrders.get(i);
            if (CollectionUtils.isEmpty(workOrderPOList)) {
                continue;
            }
            WorkOrderPO key = workOrderPOList.get(0);
            List<WorkOrderPO> value = Lists.newArrayList();
            if (canSplitByAverage(routingYieldResourceMap, workOrderPOList, combinationSmallWorkOrders)) {
                List<WorkOrderPO> popCombinationWorkOrders =
                        popCombinationWorkOrdersAverage(
                                ruleEncodingsMap,
                                routingYieldResourceMap,
                                workOrderPOList,
                                combinationSmallWorkOrders);
                if (CollectionUtils.isEmpty(popCombinationWorkOrders)) {
                    continue;
                }
                while (CollectionUtils.isNotEmpty(popCombinationWorkOrders)) {
                    value.addAll(popCombinationWorkOrders);
                    workOrderPOList.removeAll(popCombinationWorkOrders);
                    popCombinationWorkOrders =
                            popCombinationWorkOrdersAverage(
                                    ruleEncodingsMap,
                                    routingYieldResourceMap,
                                    workOrderPOList,
                                    combinationSmallWorkOrders);
                }
            } else {
                List<WorkOrderPO> popCombinationWorkOrders =
                        popCombinationWorkOrders(
                                ruleEncodingsMap,
                                routingYieldResourceMap,
                                workOrderPOList,
                                combinationSmallWorkOrders);
                if (CollectionUtils.isEmpty(popCombinationWorkOrders)) {
                    continue;
                }
                while (CollectionUtils.isNotEmpty(popCombinationWorkOrders)) {
                    value.addAll(popCombinationWorkOrders);
                    workOrderPOList.removeAll(popCombinationWorkOrders);
                    popCombinationWorkOrders =
                            popCombinationWorkOrders(
                                    ruleEncodingsMap,
                                    routingYieldResourceMap,
                                    workOrderPOList,
                                    combinationSmallWorkOrders);
                }
            }
            result.put(key, value);
            if (CollectionUtils.isNotEmpty(value)) {
                String countingUnitId = key.getOrderNo() + "&" + physicalResourceId;
                for (WorkOrderPO po : value) {
                    po.setCountingUnitId(countingUnitId);
                }
            }
        }
        return result;
    }

    /**
     * 直接按单倍换模量直接拆分
     *
     * @param ruleEncodingsMap
     * @param routingYieldResourceMap
     * @param workOrderPOList
     * @param combinationSmallWorkOrders
     * @return
     */
    private List<WorkOrderPO> popCombinationWorkOrdersAverage(
            Map<String, RuleEncodingsVO> ruleEncodingsMap,
            Map<String, SplitStepResourceResultDTO> routingYieldResourceMap,
            List<WorkOrderPO> workOrderPOList,
            List<List<WorkOrderPO>> combinationSmallWorkOrders) {
        List<WorkOrderPO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(combinationSmallWorkOrders)) {
            return workOrderPOList;
        }
        Duration totalDuration = Duration.ZERO;
        for (WorkOrderPO workOrderPO : workOrderPOList) {
            SplitStepResourceResultDTO splitStepResourceResultDTO =
                    routingYieldResourceMap.get(workOrderPO.getId());
            if (Objects.isNull(splitStepResourceResultDTO)) {
                continue;
            }
            SplitResourceDTO splitResource = splitStepResourceResultDTO.getSplitResource();
            if (Objects.isNull(splitResource)) {
                continue;
            }
            BigDecimal yield = splitStepResourceResultDTO.getYield();
            BigDecimal quantity = workOrderPO.getQuantity();
            Duration duration =
                    getWorkOrderDuration(quantity, yield, splitResource.getUnitProductionTime());
            totalDuration = totalDuration.plus(duration);
        }
        if (totalDuration.compareTo(Duration.ofHours(SINGLE_HOUR * 2)) < 0) {
            return workOrderPOList;
        }
        Duration singleDuration = Duration.ofHours(SINGLE_HOUR);
        for (WorkOrderPO orderPO : workOrderPOList) {
            if (singleDuration.compareTo(Duration.ZERO) <= 0) {
                break;
            }
            SplitStepResourceResultDTO splitStepResourceResultDTO =
                    routingYieldResourceMap.get(orderPO.getId());
            if (Objects.isNull(splitStepResourceResultDTO)) {
                continue;
            }
            SplitResourceDTO splitResource = splitStepResourceResultDTO.getSplitResource();
            if (Objects.isNull(splitResource)) {
                continue;
            }
            BigDecimal unitProductionTime = splitResource.getUnitProductionTime();
            BigDecimal yield = splitStepResourceResultDTO.getYield();
            BigDecimal quantity = orderPO.getQuantity();
            Duration duration = getWorkOrderDuration(quantity, yield, unitProductionTime);
            if (duration.compareTo(singleDuration) < 0) {
                result.add(orderPO);
                singleDuration = singleDuration.minus(duration);
            } else {
                BigDecimal gapQuantity = getDurationQuantity(singleDuration, unitProductionTime, yield);
                // 拆，缺口数量
                orderPO.setQuantity(orderPO.getQuantity().subtract(gapQuantity));
                WorkOrderPO newOrderPO = new WorkOrderPO();
                BeanUtil.copyProperties(orderPO, newOrderPO);
                newOrderPO.setQuantity(gapQuantity);
                newOrderPO.setId(UUIDUtil.getUUID());
                newOrderPO.setOrderNo(this.getWorkOrderCode(ruleEncodingsMap));
                newOrderPO.setRemark(orderPO.getRemark() + "；根据" + orderPO.getOrderNo() + "拆分");
                result.add(newOrderPO);
                singleDuration = Duration.ZERO;
            }
        }
        if (CollectionUtils.isNotEmpty(combinationSmallWorkOrders)) {
            List<WorkOrderPO> smallWorkOrders = combinationSmallWorkOrders.remove(0);
            result.addAll(smallWorkOrders);
        }
        return result;
    }

    /**
     * 大单是否可以按单倍换模时间均分
     *
     * @param routingYieldResourceMap
     * @param workOrderPOList
     * @param combinationSmallWorkOrders
     * @return
     */
    private Boolean canSplitByAverage(
            Map<String, SplitStepResourceResultDTO> routingYieldResourceMap,
            List<WorkOrderPO> workOrderPOList,
            List<List<WorkOrderPO>> combinationSmallWorkOrders) {
        if (CollectionUtils.isEmpty(workOrderPOList)
                || CollectionUtils.isEmpty(combinationSmallWorkOrders)) {
            return false;
        }
        Duration totalDuration = Duration.ZERO;
        for (WorkOrderPO workOrderPO : workOrderPOList) {
            SplitStepResourceResultDTO splitStepResourceResultDTO =
                    routingYieldResourceMap.get(workOrderPO.getId());
            if (Objects.isNull(splitStepResourceResultDTO)) {
                continue;
            }
            SplitResourceDTO splitResource = splitStepResourceResultDTO.getSplitResource();
            if (Objects.isNull(splitResource)) {
                continue;
            }
            BigDecimal yield = splitStepResourceResultDTO.getYield();
            BigDecimal quantity = workOrderPO.getQuantity();
            Duration duration =
                    getWorkOrderDuration(quantity, yield, splitResource.getUnitProductionTime());
            totalDuration = totalDuration.plus(duration);
        }
        long totalSeconds = totalDuration.getSeconds();
        int period = (int) totalSeconds / (SINGLE_HOUR * 3600);
        return (period - 1) < combinationSmallWorkOrders.size();
    }

    /**
     * 获取大单和小单
     *
     * @param ruleEncodingsMap
     * @param routingYieldResourceMap
     * @param workOrderPOList
     * @param combinationSmallWorkOrders
     * @return
     */
    private List<WorkOrderPO> popCombinationWorkOrders(
            Map<String, RuleEncodingsVO> ruleEncodingsMap,
            Map<String, SplitStepResourceResultDTO> routingYieldResourceMap,
            List<WorkOrderPO> workOrderPOList,
            List<List<WorkOrderPO>> combinationSmallWorkOrders) {
        List<WorkOrderPO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(combinationSmallWorkOrders)) {
            return workOrderPOList;
        }
        Duration totalDuration = Duration.ZERO;
        for (WorkOrderPO workOrderPO : workOrderPOList) {
            SplitStepResourceResultDTO splitStepResourceResultDTO =
                    routingYieldResourceMap.get(workOrderPO.getId());
            if (Objects.isNull(splitStepResourceResultDTO)) {
                continue;
            }
            SplitResourceDTO splitResource = splitStepResourceResultDTO.getSplitResource();
            if (Objects.isNull(splitResource)) {
                continue;
            }
            BigDecimal yield = splitStepResourceResultDTO.getYield();
            BigDecimal quantity = workOrderPO.getQuantity();
            Duration duration =
                    getWorkOrderDuration(quantity, yield, splitResource.getUnitProductionTime());
            totalDuration = totalDuration.plus(duration);
        }
        if (totalDuration.compareTo(Duration.ofHours(SINGLE_HOUR * 2)) < 0) {
            return workOrderPOList;
        }
        Duration singleDuration = Duration.ofHours(SINGLE_HOUR);
        Duration calcDuration = Duration.ZERO;
        for (WorkOrderPO workOrderPO : workOrderPOList) {
            // 制造订单的数量
            BigDecimal quantity = workOrderPO.getQuantity();
            SplitStepResourceResultDTO splitStepResourceResultDTO =
                    routingYieldResourceMap.get(workOrderPO.getId());
            if (Objects.isNull(splitStepResourceResultDTO)) {
                continue;
            }
            SplitResourceDTO splitResource = splitStepResourceResultDTO.getSplitResource();
            if (Objects.isNull(splitResource)) {
                continue;
            }
            BigDecimal unitProductionTime = splitResource.getUnitProductionTime();
            BigDecimal yield = splitStepResourceResultDTO.getYield();
            Duration duration = getWorkOrderDuration(quantity, yield, unitProductionTime);
            if (calcDuration.plus(duration).compareTo(singleDuration) <= 0) {
                calcDuration = calcDuration.plus(duration);
                totalDuration = totalDuration.minus(duration);
                result.add(workOrderPO);
                if (calcDuration.compareTo(singleDuration) >= 0) {
                    if (CollectionUtils.isNotEmpty(combinationSmallWorkOrders)) {
                        List<WorkOrderPO> smallWorkOrders = combinationSmallWorkOrders.remove(0);
                        result.addAll(smallWorkOrders);
                    }
                    break;
                }
            } else {
                // 缺口数量
                Duration gapDuration = singleDuration.minus(calcDuration);
                BigDecimal gapQuantity = getDurationQuantity(gapDuration, unitProductionTime, yield);
                // 排除订单gap剩余数量
                Duration remainCurrentDuration = totalDuration.minus(gapDuration);
                // 排除订单剩余数量
                Duration remainDuration = totalDuration.minus(duration);
                if (duration.compareTo(singleDuration.plus(singleDuration)) < 0
                        && remainDuration.compareTo(singleDuration) >= 0
                        || remainCurrentDuration.compareTo(singleDuration) < 0) {
                    result.add(workOrderPO);
                } else {
                    // 拆，缺口数量
                    workOrderPO.setQuantity(workOrderPO.getQuantity().subtract(gapQuantity));
                    WorkOrderPO orderPO = new WorkOrderPO();
                    BeanUtil.copyProperties(workOrderPO, orderPO);
                    orderPO.setQuantity(gapQuantity);
                    orderPO.setId(UUIDUtil.getUUID());
                    orderPO.setOrderNo(this.getWorkOrderCode(ruleEncodingsMap));
                    orderPO.setRemark(orderPO.getRemark() + "；根据" + workOrderPO.getOrderNo() + "拆分");
                    result.add(orderPO);
                }
                if (CollectionUtils.isNotEmpty(combinationSmallWorkOrders)) {
                    List<WorkOrderPO> smallWorkOrders = combinationSmallWorkOrders.remove(0);
                    result.addAll(smallWorkOrders);
                }
                break;
            }
        }
        return result;
    }

    /**
     * 制造订单大单拆小单
     *
     * @param mpsAnalysisContext 应用上下文
     * @param mpsAlgorithmOutput MPS结果
     */
    @Override
    protected void splitWorkOrder(
            MpsAnalysisContext mpsAnalysisContext, RzzMpsAlgorithmOutput mpsAlgorithmOutput) {
        Date date = new Date();
        List<WorkOrderPO> createWorkOrderList = mpsAnalysisContext.getCreateWorkOrderList();
        if (CollectionUtils.isEmpty(createWorkOrderList)) {
            return;
        }
        // 清除历史优先级
        clearWorkOrderPriority(mpsAnalysisContext);
        // 计算制造订单的最早开始时间
        fillWorkOrderEarliestStartTime(mpsAnalysisContext, createWorkOrderList);
        // 制造订单排序
        List<WorkOrderPO> sortWorkOrders = getSortWorkOrders(mpsAnalysisContext, createWorkOrderList);
        Integer maxPriority =
                Optional.ofNullable(masterPlanExtDao.selectWorkOrderMaxPriority()).orElse(0);
        // 重新设置制造订单的排序信息
        AtomicInteger priority = new AtomicInteger(maxPriority);
        for (WorkOrderPO sortedWorkOrder : sortWorkOrders) {
            sortedWorkOrder.setPriority(priority.incrementAndGet());
            sortedWorkOrder.setRemark(
                    sortedWorkOrder.getRemark() + "；原优先级：" + sortedWorkOrder.getPriority());
        }
        // 清除制造订单的最早开始时间
        // clearWorkOrderEarliestStartTime(createWorkOrderList);
        List<String> routingIds =
                sortWorkOrders.stream()
                        .map(WorkOrderPO::getRoutingId)
                        .distinct()
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(routingIds)) {
            // 没有工艺路径
            return;
        }
        List<SplitRoutingStepResourceDTO> splitRoutingStepResources =
                masterPlanExtDao.getSplitRoutingStepResources(routingIds);
        if (CollectionUtils.isEmpty(splitRoutingStepResources)) {
            // 没有工艺路径对应资源信息
            return;
        }
        // 查询S1YZ主资源下所有物理资源
        List<String> mainYztResourceIds = Lists.newArrayList();
        List<CollectionValueVO> mainYztResources = ipsFeign.getByCollectionCode("S1YZ");
        if (CollectionUtils.isNotEmpty(mainYztResources)) {
            String collectionValue = mainYztResources.get(0).getCollectionValue();
            mainYztResourceIds.addAll(
                    Optional.ofNullable(operationTaskExtDao.selectMainYztResource(collectionValue))
                            .orElse(Lists.newArrayList()));
        }
        if (CollectionUtils.isEmpty(mainYztResourceIds)) {
            // 没有压制炉资源
            return;
        }
        // 制造订单推荐资源 key=workOrderId, value=physicalResourceId
        Map<String, String> workOrderOnResourceMap = mpsAnalysisContext.getWorkOrderOnResourceMap();
        Map<String, SplitStepResourceResultDTO> routingYieldResourceMap =
                getRoutingYieldResourceMap(
                        sortWorkOrders, splitRoutingStepResources, workOrderOnResourceMap);
        Map<String, List<WorkOrderPO>> sortedYztWorkOrderMap = MapUtil.newHashMap();
        for (WorkOrderPO sortWorkOrder : sortWorkOrders) {
            String workOrderId = sortWorkOrder.getId();
            SplitStepResourceResultDTO splitStepResourceResultDTO =
                    routingYieldResourceMap.get(workOrderId);
            if (Objects.isNull(splitStepResourceResultDTO)) {
                continue;
            }
            String physicalResourceId = splitStepResourceResultDTO.getSplitResource().getId();
            if (!mainYztResourceIds.contains(physicalResourceId)) {
                continue;
            }
            List<WorkOrderPO> sortYztWorkOrders =
                    sortedYztWorkOrderMap.getOrDefault(physicalResourceId, Lists.newArrayList());
            sortYztWorkOrders.add(sortWorkOrder);
            sortedYztWorkOrderMap.put(physicalResourceId, sortYztWorkOrders);
        }
        if (MapUtil.isEmpty(sortedYztWorkOrderMap)) {
            // 不存在需要拆单的制造订单
            return;
        }
        Map<String, RuleEncodingsVO> ruleEncodingsMap = mpsAnalysisContext.getRuleEncodingsMap();
        Map<String, NewProductStockPointVO> productMap = mpsAnalysisContext.getProductMap();
        Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap =
                mpsAnalysisContext.getProductStockPointBaseMap();
        Map<WorkOrderPO, List<WorkOrderPO>> remainSmallWorkOrdersMap = MapUtil.newHashMap();
        // 获取插单结果
        Map<WorkOrderPO, List<WorkOrderPO>> splitWorkOrderMap =
                getCombinationSplitWorkOrders(
                        ruleEncodingsMap,
                        productMap,
                        productStockPointBaseMap,
                        sortedYztWorkOrderMap,
                        routingYieldResourceMap,
                        remainSmallWorkOrdersMap);
        // 将插单结果调整至原排序制造订单结果里面
        for (Map.Entry<WorkOrderPO, List<WorkOrderPO>> entry : splitWorkOrderMap.entrySet()) {
            WorkOrderPO key = entry.getKey();
            List<WorkOrderPO> value = entry.getValue();
            if (CollectionUtils.isNotEmpty(value)) {
                sortWorkOrders.removeAll(value.stream().filter(x -> x != key).collect(Collectors.toList()));
            }
            int index = sortWorkOrders.indexOf(key);
            sortWorkOrders.remove(key);
            sortWorkOrders.addAll(index, value);
        }
        // 重新调整小单的顺序，将小单堆在后面
        if (MapUtil.isNotEmpty(remainSmallWorkOrdersMap)) {
            for (Map.Entry<WorkOrderPO, List<WorkOrderPO>> entry : remainSmallWorkOrdersMap.entrySet()) {
                if (!sortWorkOrders.contains(entry.getKey())) {
                    continue;
                }
                List<WorkOrderPO> smallOrders = entry.getValue();
                sortWorkOrders.removeAll(smallOrders);
                int index = sortWorkOrders.indexOf(entry.getKey());
                sortWorkOrders.addAll(index + 1, smallOrders);
            }
        }
        priority = new AtomicInteger(maxPriority);
        for (WorkOrderPO sortedWorkOrder : sortWorkOrders) {
            sortedWorkOrder.setPriority(priority.incrementAndGet());
        }
        log.info("拆批插单--拆单后制造订单是否完全包含拆弹前制造订单：{}", sortWorkOrders.containsAll(createWorkOrderList));
        log.info("拆批插单--拆单前制造订单数量：{}条", createWorkOrderList.size());
        createWorkOrderList.clear();
        createWorkOrderList.addAll(sortWorkOrders);
        log.info("拆批插单--拆单后制造订单数量：{}条", createWorkOrderList.size());
        mpsAnalysisContext
                .getAlgorithmStepLogDTOList()
                .add(
                        getStepLog(
                                "MPS拆单",
                                MPS_MODULE,
                                mpsAnalysisContext.getAlgorithmLog().getId(),
                                date,
                                new Date()));
    }

    /**
     * 将小单堆在大单后
     *
     * @param workOrderPOS
     * @param lastBigWorkOrder,
     * @param remainSmallWorkOrders
     */
    private Map<WorkOrderPO, List<WorkOrderPO>> getResetSmallOrderMap(
            List<WorkOrderPO> workOrderPOS,
            WorkOrderPO lastBigWorkOrder,
            List<List<WorkOrderPO>> remainSmallWorkOrders) {
        HashMap<WorkOrderPO, List<WorkOrderPO>> result = MapUtil.newHashMap();
        if (CollectionUtils.isEmpty(remainSmallWorkOrders)) {
            return result;
        }
        List<WorkOrderPO> remainOrders =
                remainSmallWorkOrders.stream().flatMap(List::stream).collect(Collectors.toList());
        List<WorkOrderPO> matchSmallOrders = Lists.newArrayList();
        int index = workOrderPOS.indexOf(lastBigWorkOrder);
        for (int i = 0; i < index; i++) {
            WorkOrderPO orderPO = workOrderPOS.get(i);
            if (remainOrders.contains(orderPO)) {
                matchSmallOrders.add(orderPO);
            }
        }
        if (CollectionUtils.isNotEmpty(matchSmallOrders)) {
            result.put(lastBigWorkOrder, matchSmallOrders);
        }
        return result;
    }

    private void clearWorkOrderPriority(MpsAnalysisContext mpsAnalysisContext) {
        List<WorkOrderPO> workOrderPOS = mpsAnalysisContext.getWorkOrderAll();
        if (CollectionUtils.isEmpty(workOrderPOS)) {
            return;
        }
        List<String> workOrderIds =
                workOrderPOS.stream().map(WorkOrderPO::getId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workOrderIds)) {
            return;
        }
        List<List<String>> partition = com.google.common.collect.Lists.partition(workOrderIds, 2000);
        try {
            for (List<String> orderIds : partition) {
                masterPlanExtDao.clearBatchWorkOrderPriority(orderIds);
            }
        } catch (Exception e) {
            log.error("清除制造订单优先级失败：{}", e.getMessage());
        }
        for (WorkOrderPO workOrderPO : workOrderPOS) {
            workOrderPO.setPriority(null);
        }
    }

    /**
     * 获取插单结果
     *
     * @param sortedYztWorkOrderMap
     * @param routingYieldResourceMap
     * @return
     */
    private Map<WorkOrderPO, List<WorkOrderPO>> getCombinationSplitWorkOrders(
            Map<String, RuleEncodingsVO> ruleEncodingsMap,
            Map<String, NewProductStockPointVO> productMap,
            Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap,
            Map<String, List<WorkOrderPO>> sortedYztWorkOrderMap,
            Map<String, SplitStepResourceResultDTO> routingYieldResourceMap,
            Map<WorkOrderPO, List<WorkOrderPO>> remainSmallWorkOrdersMap) {
        Map<WorkOrderPO, List<WorkOrderPO>> result = MapUtil.newHashMap();
        for (Map.Entry<String, List<WorkOrderPO>> entry : sortedYztWorkOrderMap.entrySet()) {
            String physicalResourceId = entry.getKey();
            log.info("拆批插单--处理资源：{}", physicalResourceId);
            List<WorkOrderPO> workOrderPOList = entry.getValue();
            if (CollectionUtils.isEmpty(workOrderPOList)) {
                continue;
            }
            for (WorkOrderPO order : workOrderPOList) {
                String countingUnitId = order.getOrderNo() + "&" + physicalResourceId;
                order.setCountingUnitId(countingUnitId);
            }
            // 组合大单
            List<List<WorkOrderPO>> combinationBigWorkOrders = Lists.newArrayList();
            // 组合小单
            List<List<WorkOrderPO>> combinationSmallWorkOrders = Lists.newArrayList();
            // 获取组合大单和小单
            getCombinationBigWorkOrders(
                    routingYieldResourceMap,
                    productMap,
                    productStockPointBaseMap,
                    workOrderPOList,
                    combinationBigWorkOrders,
                    combinationSmallWorkOrders);
            if (CollectionUtils.isEmpty(combinationBigWorkOrders)
                    || CollectionUtils.isEmpty(combinationSmallWorkOrders)) {
                continue;
            }
            List<WorkOrderPO> lastWorkOrders =
                    combinationBigWorkOrders.get(combinationBigWorkOrders.size() - 1);
            WorkOrderPO lastBigWorkOrder = lastWorkOrders.get(lastWorkOrders.size() - 1);
            log.info("拆批插单--组合大单共有：{}组", combinationBigWorkOrders.size());
            for (List<WorkOrderPO> pos : combinationBigWorkOrders) {
                if (CollectionUtils.isEmpty(pos)) {
                    continue;
                }
                String workOrderIds =
                        pos.stream().map(WorkOrderPO::getOrderNo).collect(Collectors.joining("，"));
                log.info("拆批插单--资源：{}--组合大单有：{}", physicalResourceId, workOrderIds);
            }
            log.info("拆批插单--小单共有：{}组", combinationSmallWorkOrders.size());
            for (List<WorkOrderPO> pos : combinationSmallWorkOrders) {
                if (CollectionUtils.isEmpty(pos)) {
                    continue;
                }
                String smallWorkOrderIds =
                        pos.stream().map(WorkOrderPO::getOrderNo).collect(Collectors.joining("，"));
                log.info("拆批插单--资源：{}--小单有：{}", physicalResourceId, smallWorkOrderIds);
            }
            Map<WorkOrderPO, List<WorkOrderPO>> resourceWorkOrdersMap =
                    getSplitWorkOrders(
                            physicalResourceId,
                            routingYieldResourceMap,
                            ruleEncodingsMap,
                            combinationBigWorkOrders,
                            combinationSmallWorkOrders);
            result.putAll(resourceWorkOrdersMap);
            // 将剩余小单插在大单后面
            if (MapUtil.isNotEmpty(resourceWorkOrdersMap)
                    && CollectionUtils.isNotEmpty(combinationSmallWorkOrders)) {
                remainSmallWorkOrdersMap.putAll(
                        getResetSmallOrderMap(workOrderPOList, lastBigWorkOrder, combinationSmallWorkOrders));
            }
        }
        // 更新小单最早开始时间
        if (MapUtil.isNotEmpty(result)) {
            for (Map.Entry<WorkOrderPO, List<WorkOrderPO>> entry : result.entrySet()) {
                List<WorkOrderPO> value = entry.getValue();
                Date earliestStartTime = entry.getKey().getEarliestStartTime();
                if (CollectionUtils.isEmpty(value)) {
                    continue;
                }
                for (WorkOrderPO order : value) {
                    order.setEarliestStartTime(earliestStartTime);
                }
            }
        }
        return result;
    }

    /**
     * 获取组合大单
     *
     * @param workOrderPOList
     * @return
     */
    private void getCombinationBigWorkOrders(
            Map<String, SplitStepResourceResultDTO> routingYieldResourceMap,
            Map<String, NewProductStockPointVO> productMap,
            Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap,
            List<WorkOrderPO> workOrderPOList,
            List<List<WorkOrderPO>> combinationBigWorkOrders,
            List<List<WorkOrderPO>> combinationSmallWorkOrders) {
        Map<Integer, List<WorkOrderPO>> combinationMap = MapUtil.newHashMap();
        Integer index = 0;
        String standardResourceId = "";
        for (WorkOrderPO workOrderPO : workOrderPOList) {
            String gzmj =
                    getGzmjByWorkOrder(productMap, productStockPointBaseMap, workOrderPO.getProductId());
            if (StringUtils.isNotBlank(standardResourceId) && standardResourceId.equals(gzmj)) {
                List<WorkOrderPO> combinationWorkOrders =
                        combinationMap.getOrDefault(index, Lists.newArrayList());
                combinationWorkOrders.add(workOrderPO);
                combinationMap.put(index, combinationWorkOrders);
            } else {
                index++;
                standardResourceId = gzmj;
                List<WorkOrderPO> combinationWorkOrders =
                        combinationMap.getOrDefault(index, Lists.newArrayList());
                combinationWorkOrders.add(workOrderPO);
                combinationMap.put(index, combinationWorkOrders);
            }
        }
        // 两倍换模时间
        Duration doubleDuration = Duration.ofHours(SINGLE_HOUR * 2);
        // 单倍换模时间
        Duration singleDuration = Duration.ofHours(SINGLE_HOUR);
        for (Map.Entry<Integer, List<WorkOrderPO>> entry : combinationMap.entrySet()) {
            List<WorkOrderPO> combinationWorkOrders = entry.getValue();
            if (CollectionUtils.isEmpty(combinationWorkOrders)) {
                continue;
            }
            Duration totalDuration = Duration.ZERO;
            Iterator<WorkOrderPO> iterator = combinationWorkOrders.iterator();
            while (iterator.hasNext()) {
                WorkOrderPO workOrderPO = iterator.next();
                SplitStepResourceResultDTO resourceResultDTO =
                        routingYieldResourceMap.get(workOrderPO.getId());
                if (Objects.isNull(resourceResultDTO)) {
                    iterator.remove();
                    continue;
                }
                BigDecimal unitProductionTime =
                        resourceResultDTO.getSplitResource().getUnitProductionTime();
                // 成品率
                BigDecimal yield = resourceResultDTO.getYield();
                BigDecimal quantity = workOrderPO.getQuantity();
                Duration workOrderDuration = getWorkOrderDuration(quantity, yield, unitProductionTime);
                totalDuration = totalDuration.plus(workOrderDuration);
                workOrderPO.setRemark(workOrderPO.getRemark() + "；订单原数量：" + quantity.intValue());
            }
            // 大于等于2倍换模时间
            if (totalDuration.compareTo(doubleDuration) >= 0) {
                combinationBigWorkOrders.add(combinationWorkOrders);
            } else {
                if (totalDuration.compareTo(singleDuration) < 0) {
                    combinationSmallWorkOrders.add(combinationWorkOrders);
                }
            }
        }
    }

    /**
     * 获取工装模具
     *
     * @param productMap
     * @param productStockPointBaseMap
     * @param productId
     * @return
     */
    private String getGzmjByWorkOrder(
            Map<String, NewProductStockPointVO> productMap,
            Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap,
            String productId) {
        NewProductStockPointVO pointVO = productMap.get(productId);
        if (Objects.isNull(pointVO)) {
            return null;
        }
        // 产品类型
        String cplx = pointVO.getVehicleModelCode();
        String gzmj = cplx;
        MdsProductStockPointBaseVO pointBaseVO = productStockPointBaseMap.get(pointVO.getProductCode());
        if (Objects.nonNull(pointBaseVO)) {
            // 工装模具
            String workOrderStandardResourceId = pointBaseVO.getStandardResourceId();
            if (StringUtils.isNotBlank(workOrderStandardResourceId)
                    && !"/".equals(workOrderStandardResourceId)) {
                gzmj = workOrderStandardResourceId;
            }
        }
        return StringUtils.isBlank(gzmj) ? "" : gzmj;
    }

    /**
     * 单倍换模数量（向下取整）
     *
     * @param unitProductionTime
     * @return
     */
    private BigDecimal getSingleQuantity(BigDecimal unitProductionTime, BigDecimal yield) {
        BigDecimal singleTime = new BigDecimal(4 * 60 * 60);
        return yield.multiply(singleTime).divide(unitProductionTime, 0, RoundingMode.DOWN);
    }

    /**
     * 获取指定时长的订单数量
     *
     * @param duration
     * @param unitProductionTime
     * @param yield
     * @return
     */
    private BigDecimal getDurationQuantity(
            Duration duration, BigDecimal unitProductionTime, BigDecimal yield) {
        BigDecimal singleTime = new BigDecimal(duration.getSeconds());
        return yield.multiply(singleTime).divide(unitProductionTime, 0, RoundingMode.DOWN);
    }

    /**
     * 获取制造订单时长
     *
     * @param quantity
     * @param yield
     * @param unitProductionTime
     * @return
     */
    private Duration getWorkOrderDuration(
            BigDecimal quantity, BigDecimal yield, BigDecimal unitProductionTime) {
        if (Objects.isNull(quantity) || Objects.isNull(yield) || Objects.isNull(unitProductionTime)) {
            return Duration.ZERO;
        }
        BigDecimal operationQuantity = quantity.divide(yield, 0, RoundingMode.DOWN);
        return Duration.ofSeconds(operationQuantity.multiply(unitProductionTime).longValue());
    }

    /**
     * 获取累计成品率、优先级最高资源
     *
     * @param splitRoutingStepResources
     * @return
     */
    private Map<String, SplitStepResourceResultDTO> getRoutingYieldResourceMap(
            List<WorkOrderPO> sortWorkOrders,
            List<SplitRoutingStepResourceDTO> splitRoutingStepResources,
            Map<String, String> workOrderOnResourceMap) {
        Map<String, SplitStepResourceResultDTO> routingYieldResourceMap = MapUtil.newHashMap();
        if (CollectionUtils.isEmpty(sortWorkOrders)
                || CollectionUtils.isEmpty(splitRoutingStepResources)) {
            return routingYieldResourceMap;
        }
        Map<String, List<SplitRoutingStepResourceDTO>> routingMap =
                splitRoutingStepResources.stream()
                        .collect(Collectors.groupingBy(SplitRoutingStepResourceDTO::getRoutingId));
        for (WorkOrderPO sortWorkOrder : sortWorkOrders) {
            String routingId = sortWorkOrder.getRoutingId();
            String workOrderId = sortWorkOrder.getId();
            // mps推荐资源
            String recommendResourceId = workOrderOnResourceMap.get(workOrderId);
            List<SplitRoutingStepResourceDTO> stepResources = routingMap.get(routingId);
            if (CollectionUtils.isEmpty(stepResources)) {
                continue;
            }
            if (StringUtils.isBlank(recommendResourceId)) {
                sortWorkOrder.setRemark(sortWorkOrder.getRemark() + "；MPS推荐资源为空");
                continue;
            }
            List<SplitRoutingStepResourceDTO> sortedStepResources =
                    stepResources.stream()
                            .sorted(
                                    Comparator.comparing(SplitRoutingStepResourceDTO::getStepSequenceNo).reversed())
                            .collect(Collectors.toList());
            // 累计成品率
            BigDecimal accumulateYield = BigDecimal.ONE;
            SplitResourceDTO splitResourceDTO = null;
            for (SplitRoutingStepResourceDTO stepResource : sortedStepResources) {
                BigDecimal yield =
                        Objects.isNull(stepResource.getYield()) ? BigDecimal.ONE : stepResource.getYield();
                accumulateYield = accumulateYield.multiply(yield);
                if (!StandardStepEnum.FORMING_PROCESS
                        .getCode()
                        .equals(stepResource.getStandardStepType())) {
                    continue;
                }
                List<SplitResourceDTO> physicalResources = stepResource.getPhysicalResources();
                if (CollectionUtils.isEmpty(physicalResources)) {
                    continue;
                }
                Optional<SplitResourceDTO> firstResource =
                        physicalResources.stream()
                                .filter(x -> recommendResourceId.equals(x.getId()))
                                .min(
                                        Comparator.comparing(SplitResourceDTO::getPriority)
                                                .thenComparing(SplitResourceDTO::getId));
                if (firstResource.isPresent()) {
                    splitResourceDTO = firstResource.get();
                } else {
                    sortWorkOrder.setRemark(sortWorkOrder.getRemark() + "；MPS推荐资源不存在匹配候选资源");
                }
                break;
            }
            if (Objects.isNull(splitResourceDTO)) {
                continue;
            }
            SplitStepResourceResultDTO resourceResultDTO = new SplitStepResourceResultDTO();
            resourceResultDTO.setRoutingId(routingId);
            resourceResultDTO.setYield(accumulateYield);
            resourceResultDTO.setSplitResource(splitResourceDTO);
            routingYieldResourceMap.put(workOrderId, resourceResultDTO);
        }
        return routingYieldResourceMap;
    }

    /**
     * 计算制造订单的最早开始时间
     *
     * @param mpsAnalysisContext
     * @param createWorkOrderList
     */
    private void fillWorkOrderEarliestStartTime(
            MpsAnalysisContext mpsAnalysisContext, List<WorkOrderPO> createWorkOrderList) {
        if (CollectionUtils.isEmpty(createWorkOrderList)) {
            return;
        }
        Map<String, NewProductStockPointVO> productMap = mpsAnalysisContext.getProductMap();
        List<String> productCodeList = Lists.newArrayList();
        for (WorkOrderPO workOrderPO : createWorkOrderList) {
            NewProductStockPointVO pointVO = productMap.get(workOrderPO.getProductId());
            if (Objects.nonNull(pointVO)) {
                productCodeList.add(pointVO.getProductCode());
            }
        }
        // 查询风险等级
        List<PartRiskLevelVO> partRiskLevelVOS =
                dfpFeign.selectMaterialRiskLeveByProductCodeList(null, productCodeList);
        Map<String, PartRiskLevelVO> partRiskLevelVOMap =
                CollectionUtils.isEmpty(partRiskLevelVOS)
                        ? MapUtil.newHashMap()
                        : partRiskLevelVOS.stream()
                        .collect(
                                Collectors.toMap(
                                        PartRiskLevelVO::getProductCode, Function.identity(), (k1, k2) -> k1));
        // 查询提前生产批次规则
        List<ProductAdvanceBatchRuleVO> productAdvanceBatchRuleVOS =
                productAdvanceBatchRuleService.selectAll();
        Map<String, ProductAdvanceBatchRuleVO> riskLevelAdvanceBatchRuleVOMap =
                CollectionUtils.isEmpty(productAdvanceBatchRuleVOS)
                        ? MapUtil.newHashMap()
                        : productAdvanceBatchRuleVOS.stream()
                        .filter(p -> StrUtil.isNotEmpty(p.getRiskLevel()))
                        .collect(
                                Collectors.toMap(
                                        ProductAdvanceBatchRuleVO::getRiskLevel, v -> v, (k1, k2) -> k1));
        String low = "低";
        for (WorkOrderPO workOrderPO : createWorkOrderList) {
            NewProductStockPointVO pointVO = productMap.get(workOrderPO.getProductId());
            if (Objects.isNull(pointVO)) {
                continue;
            }
            PartRiskLevelVO partRiskLevelVO = partRiskLevelVOMap.get(pointVO.getProductCode());
            ProductAdvanceBatchRuleVO advanceBatchRuleVO =
                    Objects.isNull(partRiskLevelVO)
                            ? riskLevelAdvanceBatchRuleVOMap.get(low)
                            : riskLevelAdvanceBatchRuleVOMap.get(partRiskLevelVO.getMaterialRiskLevel());
            if (Objects.isNull(advanceBatchRuleVO)) {
                continue;
            }
            Date earliestBeginTime =
                    DateUtil.offsetDay(
                            workOrderPO.getDueDate(), -advanceBatchRuleVO.getMaxProductionLeadDays().intValue());
            workOrderPO.setEarliestStartTime(earliestBeginTime);
            String remark =
                    workOrderPO.getRemark()
                            + "；原最早开始时间："
                            + DateUtil.formatDateTime(workOrderPO.getEarliestStartTime());
            workOrderPO.setRemark(remark);
        }
    }

    /**
     * 清除制造订单最早开始时间
     *
     * @param createWorkOrderList
     */
    private void clearWorkOrderEarliestStartTime(List<WorkOrderPO> createWorkOrderList) {
        if (CollectionUtils.isEmpty(createWorkOrderList)) {
            return;
        }
        for (WorkOrderPO workOrderPO : createWorkOrderList) {
            workOrderPO.setEarliestStartTime(null);
        }
    }

    /**
     * 制造订单排序
     *
     * @param mpsAnalysisContext
     * @param createWorkOrderList
     */
    private List<WorkOrderPO> getSortWorkOrders(
            MpsAnalysisContext mpsAnalysisContext, List<WorkOrderPO> createWorkOrderList) {
        Map<String, NewProductStockPointVO> productMap = mpsAnalysisContext.getProductMap();
        Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap =
                mpsAnalysisContext.getProductStockPointBaseMap();
        // 1、根据制造订单的交期排序
        CollectionUtil.sort(createWorkOrderList, Comparator.comparing(WorkOrderBasicPO::getDueDate));
        Map<String, WorkOrderPO> workOrderMap =
                createWorkOrderList.stream()
                        .collect(
                                Collectors.toMap(WorkOrderBasicPO::getId, Function.identity(), (v1, v2) -> v1));
        List<String> sortWorkOrderIds =
                createWorkOrderList.stream().map(WorkOrderPO::getId).collect(Collectors.toList());
        List<WorkOrderPO> sortedWorkOrders = Lists.newArrayList();
        // 2、根据工装模具、产品类型排序
        for (String workOrderId : sortWorkOrderIds) {
            WorkOrderPO workOrder = workOrderMap.get(workOrderId);
            NewProductStockPointVO pointVO = productMap.get(workOrder.getProductId());
            if (Objects.isNull(pointVO)) {
                sortedWorkOrders.add(workOrder);
                continue;
            }
            // 产品类型
            String cplx = pointVO.getVehicleModelCode();
            String gzmj = cplx;
            MdsProductStockPointBaseVO pointBaseVO =
                    productStockPointBaseMap.get(pointVO.getProductCode());
            if (Objects.nonNull(pointBaseVO)) {
                // 工装模具
                String standardResourceId = pointBaseVO.getStandardResourceId();
                if (StringUtils.isNotBlank(standardResourceId) && !"/".equals(standardResourceId)) {
                    gzmj = standardResourceId;
                }
            }
            if (StringUtils.isBlank(gzmj)) {
                sortedWorkOrders.add(workOrder);
                continue;
            }
            // 寻找合适地插入位置
            Integer insertIndex =
                    findInsertIndex(mpsAnalysisContext, workOrder, gzmj, cplx, sortedWorkOrders);
            if (insertIndex == -1) {
                sortedWorkOrders.add(workOrder);
            } else {
                sortedWorkOrders.add(insertIndex + 1, workOrder);
            }
        }
        return sortedWorkOrders;
    }

    /**
     * 从sortedWorkOrders中寻找合适的插入位置
     *
     * @param workOrder
     * @param gzmj
     * @param cplx
     * @param sortedWorkOrders
     * @return
     */
    private Integer findInsertIndex(
            MpsAnalysisContext mpsAnalysisContext,
            WorkOrderPO workOrder,
            String gzmj,
            String cplx,
            List<WorkOrderPO> sortedWorkOrders) {
        Map<String, NewProductStockPointVO> productMap = mpsAnalysisContext.getProductMap();
        Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap =
                mpsAnalysisContext.getProductStockPointBaseMap();
        int index = -1;
        // 是否存在同产品类型
        Boolean existSameCplx = false;
        for (int i = 0; i < sortedWorkOrders.size(); i++) {
            WorkOrderPO current = sortedWorkOrders.get(i);
            // 时间不存在交集
            if (!isTimeOverlap(workOrder, current)) {
                continue;
            }
            NewProductStockPointVO pointVO = productMap.get(current.getProductId());
            String currentCplx = Objects.isNull(pointVO) ? null : pointVO.getVehicleModelCode();
            String currentGzmj = currentCplx;
            if (Objects.nonNull(pointVO)) {
                MdsProductStockPointBaseVO pointBaseVO =
                        productStockPointBaseMap.get(pointVO.getProductCode());
                String standardResourceId =
                        Objects.isNull(pointBaseVO) ? null : pointBaseVO.getStandardResourceId();
                if (StringUtils.isNotBlank(standardResourceId) && !"/".equals(standardResourceId)) {
                    currentGzmj = standardResourceId;
                }
            }
            // 工装模具匹配
            if (StringUtils.isNotBlank(currentGzmj)
                    && StringUtils.isNotBlank(gzmj)
                    && currentGzmj.equals(gzmj)) {
                if (StringUtils.isNotBlank(currentCplx)
                        && StringUtils.isNotBlank(cplx)
                        && currentCplx.equals(cplx)) {
                    index = i;
                    existSameCplx = true;
                } else {
                    if (!existSameCplx) {
                        index = i;
                    }
                }
            }
        }
        return index;
    }

    /**
     * 判断时间是否存在交集
     *
     * @param wo1
     * @param wo2
     * @return
     */
    private boolean isTimeOverlap(WorkOrderPO wo1, WorkOrderPO wo2) {
        Date leftStartTime =
                Objects.isNull(wo1.getEarliestStartTime()) ? wo1.getDueDate() : wo1.getEarliestStartTime();
        Date rightStartTime =
                Objects.isNull(wo2.getEarliestStartTime()) ? wo2.getDueDate() : wo2.getEarliestStartTime();
        return leftStartTime.before(wo2.getDueDate()) && rightStartTime.before(wo1.getDueDate());
    }
}
