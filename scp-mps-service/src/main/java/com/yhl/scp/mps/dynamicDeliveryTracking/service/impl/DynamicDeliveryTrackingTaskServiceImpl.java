package com.yhl.scp.mps.dynamicDeliveryTracking.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.EnumUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.constants.StringConstants;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.basic.resource.convertor.PhysicalResourceConvertor;
import com.yhl.scp.mds.enums.ProductTypeEnum;
import com.yhl.scp.mds.extension.resource.domain.entity.PhysicalResourceDO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingDO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingStepDO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingStepResourceDO;
import com.yhl.scp.mds.extension.routing.vo.BomRoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.dynamicDeliveryTracking.convertor.DynamicDeliveryTrackingTaskConvertor;
import com.yhl.scp.mps.dynamicDeliveryTracking.domain.entity.DynamicDeliveryTrackingTaskDO;
import com.yhl.scp.mps.dynamicDeliveryTracking.domain.service.DynamicDeliveryTrackingTaskDomainService;
import com.yhl.scp.mps.dynamicDeliveryTracking.dto.DynamicDeliveryTrackingTaskDTO;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.dao.DynamicDeliveryTrackingTaskDao;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po.DynamicDeliveryTrackingTaskPO;
import com.yhl.scp.mps.dynamicDeliveryTracking.req.DeliveryTrackingViewReq;
import com.yhl.scp.mps.dynamicDeliveryTracking.service.DynamicDeliveryTrackingTaskService;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DeliveryTrackingNodeVO;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DeliveryTrackingResultVO;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryPolymerizationVO;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingTaskVO;
import com.yhl.scp.mps.enums.ObjectTypeEnum;
import com.yhl.scp.mps.enums.TrackingStatusEnum;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>DynamicDeliveryTrackingTaskServiceImpl</code>
 * <p>
 * 动态交付跟踪工序任务表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 13:46:30
 */
@Slf4j
@Service
public class DynamicDeliveryTrackingTaskServiceImpl extends AbstractService implements DynamicDeliveryTrackingTaskService {

    @Resource
    private DynamicDeliveryTrackingTaskDao dynamicDeliveryTrackingTaskDao;

    @Resource
    private DynamicDeliveryTrackingTaskDomainService dynamicDeliveryTrackingTaskDomainService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private IpsFeign ipsFeign;
    @Autowired
    private OperationTaskExtDao operationTaskExtDao;

    @Override
    public BaseResponse<Void> doCreate(DynamicDeliveryTrackingTaskDTO dynamicDeliveryTrackingTaskDTO) {
        // 0.数据转换
        DynamicDeliveryTrackingTaskDO dynamicDeliveryTrackingTaskDO =
                DynamicDeliveryTrackingTaskConvertor.INSTANCE.dto2Do(dynamicDeliveryTrackingTaskDTO);
        DynamicDeliveryTrackingTaskPO dynamicDeliveryTrackingTaskPO =
                DynamicDeliveryTrackingTaskConvertor.INSTANCE.dto2Po(dynamicDeliveryTrackingTaskDTO);
        // 1.数据校验
        dynamicDeliveryTrackingTaskDomainService.validation(dynamicDeliveryTrackingTaskDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(dynamicDeliveryTrackingTaskPO);
        dynamicDeliveryTrackingTaskDao.insert(dynamicDeliveryTrackingTaskPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(DynamicDeliveryTrackingTaskDTO dynamicDeliveryTrackingTaskDTO) {
        // 0.数据转换
        DynamicDeliveryTrackingTaskDO dynamicDeliveryTrackingTaskDO =
                DynamicDeliveryTrackingTaskConvertor.INSTANCE.dto2Do(dynamicDeliveryTrackingTaskDTO);
        DynamicDeliveryTrackingTaskPO dynamicDeliveryTrackingTaskPO =
                DynamicDeliveryTrackingTaskConvertor.INSTANCE.dto2Po(dynamicDeliveryTrackingTaskDTO);
        // 1.数据校验
        dynamicDeliveryTrackingTaskDomainService.validation(dynamicDeliveryTrackingTaskDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(dynamicDeliveryTrackingTaskPO);
        dynamicDeliveryTrackingTaskDao.update(dynamicDeliveryTrackingTaskPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<DynamicDeliveryTrackingTaskDTO> list) {
        List<DynamicDeliveryTrackingTaskPO> newList = DynamicDeliveryTrackingTaskConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        dynamicDeliveryTrackingTaskDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<DynamicDeliveryTrackingTaskDTO> list) {
        List<DynamicDeliveryTrackingTaskPO> newList = DynamicDeliveryTrackingTaskConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        dynamicDeliveryTrackingTaskDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return dynamicDeliveryTrackingTaskDao.deleteBatch(idList);
        }
        return dynamicDeliveryTrackingTaskDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public DynamicDeliveryTrackingTaskVO selectByPrimaryKey(String id) {
        DynamicDeliveryTrackingTaskPO po = dynamicDeliveryTrackingTaskDao.selectByPrimaryKey(id);
        return DynamicDeliveryTrackingTaskConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "DYNAMIC_DELIVERY_TRACKING_TASK")
    public List<DynamicDeliveryTrackingTaskVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "DYNAMIC_DELIVERY_TRACKING_TASK")
    public List<DynamicDeliveryTrackingTaskVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<DynamicDeliveryTrackingTaskVO> dataList = dynamicDeliveryTrackingTaskDao.selectByCondition(sortParam, queryCriteriaParam);
        DynamicDeliveryTrackingTaskServiceImpl target = SpringBeanUtils.getBean(DynamicDeliveryTrackingTaskServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<DynamicDeliveryTrackingTaskVO> selectByParams(Map<String, Object> params) {
        List<DynamicDeliveryTrackingTaskPO> list = dynamicDeliveryTrackingTaskDao.selectByParams(params);
        return DynamicDeliveryTrackingTaskConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<DynamicDeliveryTrackingTaskVO> selectVOByParams(Map<String, Object> params) {
        return dynamicDeliveryTrackingTaskDao.selectVOByParams(params);
    }

    @Override
    public List<DynamicDeliveryTrackingTaskVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.DYNAMIC_DELIVERY_TRACKING_TASK.getCode();
    }

    @Override
    public List<DynamicDeliveryTrackingTaskVO> invocation(List<DynamicDeliveryTrackingTaskVO> dataList,
                                                          Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public List<DynamicDeliveryTrackingTaskVO> selectTaskByPhysicalResourceId(String physicalResourceId) {
        if (StringUtils.isBlank(physicalResourceId)) {
            return new ArrayList<>();
        }
        List<DynamicDeliveryTrackingTaskVO> dynamicDeliveryTrackingTaskVOS = dynamicDeliveryTrackingTaskDao.selectTaskByPhysicalResourceId(physicalResourceId, true);
        DeliveryTrackingViewReq deliveryTrackingViewReq = new DeliveryTrackingViewReq();
        List<String> productCodes = StreamUtils.columnToList(dynamicDeliveryTrackingTaskVOS, DynamicDeliveryTrackingTaskVO::getProductCode)
                .stream().distinct().collect(Collectors.toList());
        deliveryTrackingViewReq.setProductCodeList(productCodes);
        List<DynamicDeliveryPolymerizationVO> dynamicDeliveryPolymerizationVOS = dynamicDeliveryTrackingTaskDao
                .selectDynamicDeliveryPolymerization(deliveryTrackingViewReq);
        Map<String, List<DynamicDeliveryPolymerizationVO>> productTaskMap = StreamUtils.mapListByColumn(dynamicDeliveryPolymerizationVOS, DynamicDeliveryPolymerizationVO::getTrackingId);
        for (DynamicDeliveryTrackingTaskVO dynamicDeliveryTrackingTaskVO : dynamicDeliveryTrackingTaskVOS) {
            String id = dynamicDeliveryTrackingTaskVO.getId();
            String trackingId = dynamicDeliveryTrackingTaskVO.getTrackingId();
            List<DynamicDeliveryPolymerizationVO> deliveryPolymerizationVOS = productTaskMap.get(trackingId);
            Map<String, List<DynamicDeliveryPolymerizationVO>> stepMap = StreamUtils.mapListByColumn(deliveryPolymerizationVOS, DynamicDeliveryPolymerizationVO::getStandardStepCode);

            DynamicDeliveryPolymerizationVO dynamicDeliveryPolymerizationVO = deliveryPolymerizationVOS.stream()
                    .filter(p -> p.getTaskId().equals(id)).findFirst().get();
            String standardStepCode = dynamicDeliveryPolymerizationVO.getStandardStepCode();
            Optional<DynamicDeliveryPolymerizationVO> deliveryPolymerizationVO = deliveryPolymerizationVOS.stream()
                    .filter(p -> Integer.parseInt(p.getStandardStepCode()) < Integer.parseInt(standardStepCode)).max(Comparator.comparing(DynamicDeliveryPolymerizationVO::getStandardStepCode));
            if (deliveryPolymerizationVO.isPresent()) {
                String preStandardStepCode = deliveryPolymerizationVO.get().getStandardStepCode();
                BigDecimal finishedQuantity = stepMap.get(preStandardStepCode).stream().map(DynamicDeliveryPolymerizationVO::getFinishedQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                dynamicDeliveryTrackingTaskVO.setPreOperationQuantity(finishedQuantity);
            }
        }
        return dynamicDeliveryTrackingTaskVOS;
    }

    @Override
    public List<String> rollingNotificationQuery(String physicalResourceId) {
        if (StringUtils.isBlank(physicalResourceId)) {
            return new ArrayList<>();
        }
        List<String> list = new ArrayList<>();
        List<DynamicDeliveryTrackingTaskVO> dynamicDeliveryTrackingTaskVOS =
                dynamicDeliveryTrackingTaskDao.selectTaskByPhysicalResourceId(physicalResourceId, false);
        if (CollectionUtils.isNotEmpty(dynamicDeliveryTrackingTaskVOS)) {

            for (DynamicDeliveryTrackingTaskVO taskVO : dynamicDeliveryTrackingTaskVOS) {
                StringBuilder stringBuilder = new StringBuilder();
                String endTimeStr = DateUtils.dateToString(taskVO.getEndTime(), DateUtils.COMMON_DATE_STR5);
                stringBuilder.append(endTimeStr).append("点前").append(taskVO.getProductCode())
                        .append("产品总交付数量").append(taskVO.getQuantity().stripTrailingZeros().toPlainString()).append("！当前已完成数量")
                        .append(taskVO.getFinishedQuantity().stripTrailingZeros().toPlainString());
                list.add(stringBuilder.toString());
            }
        }
        return list;
    }

    @Override
    public List<LabelValue<String>> listResourceDropDown() {
        List<Scenario> scenarioList = ipsNewFeign.selectDefaultByTenantId("");
        return scenarioList.stream()
                .map(item -> new LabelValue<>(item.getScenarioName(),
                        item.getDataBaseName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<DynamicDeliveryTrackingTaskVO> buildTaskByProductCodeList(List<String> productCodeList) {
        if (CollectionUtils.isEmpty(productCodeList)) {
            return Collections.emptyList();
        }
        // 查询销售组织的库存点
        List<NewStockPointVO> newStockPointVOS = newMdsFeign.selectStockPointByParams(SystemHolder.getScenario(),
                ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode()));
        if (CollectionUtils.isEmpty(newStockPointVOS)) {
            return Collections.emptyList();
        }
        List<String> saleStockPointCodeList = newStockPointVOS.stream()
                .filter(t -> StockPointTypeEnum.BC.getCode().equals(t.getStockPointType()) &&
                        StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode().equals(t.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> productStockPointVOS = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(), ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),
                "productCodeList", productCodeList));

        if (CollectionUtils.isEmpty(productStockPointVOS)) {
            return Collections.emptyList();
        }
        List<NewProductStockPointVO> saleProductStockPoint = productStockPointVOS.stream()
                .filter(t -> saleStockPointCodeList.contains(t.getStockPointCode())).collect(Collectors.toList());
        Map<String, List<NewProductStockPointVO>> allProductStockPointMap = productStockPointVOS.stream()
                .collect(Collectors.groupingBy(NewProductStockPointVO::getProductCode));
        Map<String, String> productOfIdMap = new HashMap<>();
        saleProductStockPoint.forEach(t -> {
            String[] split = t.getPoCategory().split("\\.");
            // 工艺路径对应库存点
            String stockPointCode = split[split.length - 1];
            allProductStockPointMap.get(t.getProductCode())
                    .stream().filter(item -> stockPointCode.equals(item.getStockPointCode()))
                    .findFirst().ifPresent(productStockPointVO -> productOfIdMap.put(t.getProductCode(), productStockPointVO.getId()));
        });
        Map<String, NewProductStockPointVO> stockPointProductMap = StreamUtils.mapByColumn(saleProductStockPoint, NewProductStockPointVO::getProductCode);
        List<CollectionValueVO> highPressure = ipsFeign.getByCollectionCode("HIGH_PRESSURE");
        if (CollectionUtils.isEmpty(highPressure)) {
            throw new BusinessException("HIGH_PRESSURE字典表未配置");
        }
        List<String> valueMeaningList = StreamUtils.columnToList(highPressure, CollectionValueVO::getValueMeaning);
        List<PhysicalResourceVO> physicalResourceVOS = operationTaskExtDao.selectPhysicalResourceBySequenceList(valueMeaningList);
        Map<String, PhysicalResourceVO> physicalResourceVOMap = StreamUtils.mapByColumn(physicalResourceVOS, PhysicalResourceVO::getSequenceCode);

        Map<String, CollectionValueVO> collectionValueVOMap = StreamUtils.mapByColumn(highPressure, CollectionValueVO::getCollectionValue);
        // 查询是否有多层bom
        List<BomRoutingStepInputVO> bomRoutingStepInputVOS = newMdsFeign.selectBomRoutingStepInputByParams(SystemHolder.getScenario(),
                ImmutableMap.of("productCodeList", productCodeList, "productType", ProductTypeEnum.SA.getCode()));
        Map<String, BomRoutingStepInputVO> routingStepInputVOMap = bomRoutingStepInputVOS.stream().collect(Collectors.toMap(BomRoutingStepInputVO::getSourceProductCode, Function.identity(),
                (t1, t2) -> t1));
        List<String> totalProductIdList = new ArrayList<>(productOfIdMap.values());
        if (!routingStepInputVOMap.isEmpty()) {
            List<String> inputProductCodeList = routingStepInputVOMap.values().stream().map(BomRoutingStepInputVO::getInputProductId).distinct().collect(Collectors.toList());
            totalProductIdList.addAll(inputProductCodeList);
        }

        List<DynamicDeliveryTrackingTaskVO> trackingTaskVOList = new ArrayList<>();
        // 组装工艺路径
        List<RoutingDO> routingDOS = newMdsFeign.getRoutingDOByParams(ImmutableMap.of("productIds", totalProductIdList));
        Map<String, List<RoutingDO>> routingMap = routingDOS.stream().collect(Collectors.groupingBy(RoutingDO::getProductId));
        for (String productCode : productCodeList) {
            // 原物料id
            String productId = productOfIdMap.get(productCode);
            if (!routingMap.containsKey(productId)) {
                continue;
            }

            generateTrackingTask(trackingTaskVOList, routingMap, productCode, productId, productId);
            // 把半品工艺路径也加进去
            if (routingStepInputVOMap.containsKey(productCode)) {
                String inputProductId = routingStepInputVOMap.get(productCode).getInputProductId();
                generateTrackingTask(trackingTaskVOList, routingMap, productCode, productId, inputProductId);
            }
            // 根据组织代码构建‘高压’工序
            List<RoutingDO> routingDOList = routingMap.get(productId);
            for (RoutingDO routingDO : routingDOList) {
                String stockPointId = routingDO.getStockPointId();
                if (collectionValueVOMap.containsKey(stockPointId)) {
                    String valueMeaning = collectionValueVOMap.get(stockPointId).getValueMeaning();
                    DynamicDeliveryTrackingTaskVO trackingTaskVO = new DynamicDeliveryTrackingTaskVO();
                    trackingTaskVO.setProductId(productId);
                    trackingTaskVO.setProductCode(productCode);
                    trackingTaskVO.setStandardStepId(valueMeaning);
                    trackingTaskVO.setStandardStepName("高压");
                    trackingTaskVO.setStandardStepCode(collectionValueVOMap.get(stockPointId).getValueMeaning());
                    RoutingStepResourceDO routingStepResourceDO = buildResource(physicalResourceVOMap, valueMeaning);
                    trackingTaskVO.setRoutingStepResourceDOList(ListUtil.of(routingStepResourceDO));
                    trackingTaskVOList.add(trackingTaskVO);
                }
            }
        }
        trackingTaskVOList.sort(Comparator.comparing(DynamicDeliveryTrackingTaskVO::getStandardStepCode).reversed());
        return trackingTaskVOList;
    }

    private RoutingStepResourceDO buildResource(Map<String, PhysicalResourceVO> physicalResourceVOMap, String valueMeaning) {
        PhysicalResourceVO physicalResourceVO = physicalResourceVOMap.get(valueMeaning);
        PhysicalResourceDO physicalResourceDO = PhysicalResourceConvertor.INSTANCE.vo2do(physicalResourceVO);
        RoutingStepResourceDO routingStepResourceDO = RoutingStepResourceDO.builder()
                .id(physicalResourceVO.getId())
                .physicalResourceId(physicalResourceVO.getId())
                .physicalResourceDOS(ListUtil.of(physicalResourceDO))
                .build();
        return routingStepResourceDO;
    }

    private void generateTrackingTask(List<DynamicDeliveryTrackingTaskVO> trackingTaskVOList, Map<String, List<RoutingDO>> routingMap, String productCode, String productId, String inputProductId) {
        List<RoutingDO> intputRoutingDOList = routingMap.get(inputProductId);
        for (RoutingDO routingDO : intputRoutingDOList) {
            List<RoutingStepDO> routingStepDOList = routingDO.getRoutingStepDOList();
            routingStepDOList.sort(Comparator.comparing(RoutingStepDO::getSequenceNo).reversed());
            for (RoutingStepDO routingStepDO : routingStepDOList) {
                DynamicDeliveryTrackingTaskVO trackingTaskVO = new DynamicDeliveryTrackingTaskVO();
                trackingTaskVO.setProductId(productId);
                trackingTaskVO.setProductCode(productCode);
                trackingTaskVO.setStandardStepId(routingStepDO.getId());
                trackingTaskVO.setStandardStepName(routingStepDO.getStandardStepDO().getStandardStepName());
                trackingTaskVO.setStandardStepCode(routingStepDO.getStandardStepDO().getStandardStepCode());
                trackingTaskVO.setRoutingStepResourceDOList(routingStepDO.getRoutingStepResourceDOList());
                trackingTaskVOList.add(trackingTaskVO);
            }
        }
    }

    public static void paramsCheck(DynamicDeliveryTrackingTaskVO taskVO) {
        if (taskVO == null) {
            throw new BusinessException("参数不能为空！");
        }
        if (StringUtils.isEmpty(taskVO.getStandardStepId())) {
            throw new BusinessException("工序不能为空！");
        }
        if (taskVO.getQuantity() == null || taskVO.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("数量不能为0或为空！");
        }
        if (StringUtils.isEmpty(taskVO.getPhysicalResourceId())) {
            throw new BusinessException("产线不能为空！");
        }
        if (taskVO.getStartTime() == null) {
            throw new BusinessException("生产时间不能为空！");
        }
        if (StringUtils.isEmpty(taskVO.getId()) && CollectionUtils.isEmpty(taskVO.getSubTaskVOS())) {
            // 新增的数据必须有追踪工序任务明细
            throw new BusinessException("没有设置追踪工序任务明细");
        }
    }

    @Override
    public List<DeliveryTrackingNodeVO> deliveryTrackingView(DeliveryTrackingViewReq deliveryTrackingViewReq, Pagination pagination) {
        assemblyParam(deliveryTrackingViewReq);
        if (StrUtil.isNotEmpty(deliveryTrackingViewReq.getStandardOperation()) && CollectionUtils.isEmpty(deliveryTrackingViewReq.getTrackingIds())) {
            return new ArrayList<>();
        }
        List<CollectionValueVO> showsProcesses = ipsFeign.getByCollectionCode("DELIVERY_TRACKING_OVERVIEW_SHOWS_PROCESSES");
        if (CollectionUtils.isEmpty(showsProcesses)) {
            throw new BusinessException("未维护字典表DELIVERY_TRACKING_OVERVIEW_SHOWS_PROCESSES");
        }
        List<String> showProcessesList = showsProcesses.stream().map(CollectionValueVO::getCollectionValue).collect(Collectors.toList());
        List<DynamicDeliveryPolymerizationVO> dynamicDeliveryPolymerizationVOS = dynamicDeliveryTrackingTaskDao
                .selectDynamicDeliveryPolymerization(deliveryTrackingViewReq)
                .stream().peek(p -> {
                    if (StrUtil.isBlank(p.getStandardStepId())) {
                        p.setStandardStepId(p.getStepId());
                    }
                }).collect(Collectors.toList());
        Map<String, List<DynamicDeliveryPolymerizationVO>> dynamicMap = StreamUtils.mapListByColumn(dynamicDeliveryPolymerizationVOS,
                p -> StrUtil.join(StringConstants.SPLIT_STR_2, p.getStockPointCode(), p.getStockPointName()));
        List<StandardStepVO> standardStepVOS = newMdsFeign.selectStandardStepAll(SystemHolder.getScenario());
        // 增加S1/S2高压工序
        addHighProcess(standardStepVOS);
        standardStepVOS.forEach(p -> p.setStandardStepName(p.getStandardStepName() + "生产时间"));
        Map<String, List<StandardStepVO>> standardStepMap = StreamUtils.mapListByColumn(standardStepVOS, StandardStepVO::getStockPointCode);
        List<DeliveryTrackingNodeVO> deliveryTrackingNodeVOS = new ArrayList<>();
        // 进仓工序
        String warehousedOperation = ipsFeign.getByCollectionCode("WAREHOUSING_PROCESS").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElse(null);
        for (Map.Entry<String, List<DynamicDeliveryPolymerizationVO>> entry : dynamicMap.entrySet()) {
            String key = entry.getKey();
            String stockPointCode = key.split(StringConstants.SPLIT_STR_2)[0];
            if (!standardStepMap.containsKey(stockPointCode)) {
                log.warn("组织：{}，没有标准工艺配置信息", stockPointCode);
                continue;
            }
            List<StandardStepVO> standardStepList = standardStepMap.get(stockPointCode);
            standardStepList = standardStepList.stream().filter(p -> showProcessesList.contains(p.getStandardStepName()))
                    .collect(Collectors.toList());
            standardStepList.sort(Comparator.comparing(StandardStepVO::getStandardStepCode));
            List<String> dynamicHeader = standardStepList.stream().map(
                    StandardStepVO::getStandardStepName).collect(Collectors.toList());
            dynamicHeader.add("进仓时间");
            dynamicHeader.add("发货时间");
            //构建动态内容
            List<DeliveryTrackingResultVO> deliveryTrackingResultBody = getDeliveryTrackingResultBody(standardStepList,
                    entry.getValue(), warehousedOperation);
            DeliveryTrackingNodeVO trackingNodeVO = DeliveryTrackingNodeVO.builder()
                    .node(key)
                    .dynamicHeader(dynamicHeader.stream().distinct().collect(Collectors.toList()))
                    .deliveryTrackingResultVOS(deliveryTrackingResultBody)
                    .build();
            deliveryTrackingNodeVOS.add(trackingNodeVO);
        }
        return deliveryTrackingNodeVOS;
    }

    private void addHighProcess(List<StandardStepVO> standardStepVOS) {
        List<CollectionValueVO> highPressure = ipsFeign.getByCollectionCode("HIGH_PRESSURE");
        if (CollectionUtils.isEmpty(highPressure)) {
            throw new BusinessException("HIGH_PRESSURE字典表未配置");
        }
        for (CollectionValueVO collectionValueVO : highPressure) {
            StandardStepVO standardStepVO = StandardStepVO.builder()
                    .build();
            standardStepVO.setStandardStepName("高压");
            standardStepVO.setStandardStepCode(collectionValueVO.getValueMeaning());
            standardStepVO.setStockPointCode(collectionValueVO.getCollectionValue());
            standardStepVO.setId(collectionValueVO.getValueMeaning());
            standardStepVOS.add(standardStepVO);
        }
    }

    private List<DeliveryTrackingResultVO> getDeliveryTrackingResultBody(List<StandardStepVO> standardStepList,
                                                                         List<DynamicDeliveryPolymerizationVO> value,
                                                                         String warehousedOperation) {
        List<DeliveryTrackingResultVO> deliveryTrackingResultVOList = new ArrayList<>();
        Map<String, List<DynamicDeliveryPolymerizationVO>> dynamicMap = StreamUtils.mapListByColumn(value,
                DynamicDeliveryPolymerizationVO::getTrackingId);
        for (Map.Entry<String, List<DynamicDeliveryPolymerizationVO>> entry : dynamicMap.entrySet()) {
            List<DynamicDeliveryPolymerizationVO> deliveryTask = entry.getValue()
                    .stream().filter(p -> StrUtil.isNotEmpty(p.getStandardStepId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(deliveryTask)) {
                continue;
            }
            Map<String, List<DynamicDeliveryPolymerizationVO>> taskMap = StreamUtils.mapListByColumn(deliveryTask,
                    DynamicDeliveryPolymerizationVO::getStandardStepId);
            List<DeliveryTrackingResultVO.DeliveryTrackingBody> dynamicDeliveryTrackingBody = new ArrayList<>();
            DynamicDeliveryPolymerizationVO dynamicDeliveryPolymerizationVO = deliveryTask.get(0);
            String trackingId = dynamicDeliveryPolymerizationVO.getTrackingId();
            String vehicleModelCode = dynamicDeliveryPolymerizationVO.getVehicleModelCode();
            String productCode = dynamicDeliveryPolymerizationVO.getProductCode();
            BigDecimal warehouseNumber = dynamicDeliveryPolymerizationVO.getWarehouseNumber().setScale(0, RoundingMode.HALF_UP);
            Date deliveryTime = dynamicDeliveryPolymerizationVO.getDeliveryTime();
            Date warehousedTime = dynamicDeliveryPolymerizationVO.getWarehousedTime();
            String trackingStatus = dynamicDeliveryPolymerizationVO.getTrackingStatus();
            for (StandardStepVO standardStepVO : standardStepList) {
                String stepId = standardStepVO.getId();
                if (!taskMap.containsKey(stepId)) {
                    continue;
                }
                DeliveryTrackingResultVO.DeliveryTrackingBody deliveryTrackingBody = getDeliveryBody(taskMap, stepId,
                        standardStepVO.getStandardStepName(), Boolean.FALSE);
                dynamicDeliveryTrackingBody.add(deliveryTrackingBody);
            }
            // 构建 高压/进仓/发货时间
            addDynamicWarehouseTime(dynamicDeliveryTrackingBody, deliveryTime, warehousedOperation, taskMap);
            DeliveryTrackingResultVO deliveryTrackingResultVO = DeliveryTrackingResultVO.builder()
                    .trackingId(trackingId)
                    .vehicleModelCode(vehicleModelCode)
                    .productCode(productCode)
                    .warehouseNumber(warehouseNumber)
                    .warehouseTime(DateUtils.dateToString(warehousedTime, DateUtils.COMMON_DATE_STR5))
                    .deliveryTime(DateUtils.dateToString(deliveryTime, DateUtils.COMMON_DATE_STR5))
                    .dynamicDeliveryTrackingBody(dynamicDeliveryTrackingBody)
                    .trackingStatus(EnumUtils.getDescByCode(TrackingStatusEnum.class, trackingStatus))
                    .build();
            deliveryTrackingResultVOList.add(deliveryTrackingResultVO);
        }
        return deliveryTrackingResultVOList;
    }

    private DeliveryTrackingResultVO.DeliveryTrackingBody getDeliveryBody(Map<String, List<DynamicDeliveryPolymerizationVO>> taskMap,
                                                                          String stepId,
                                                                          String standardStepName, Boolean aFalse) {
        List<DynamicDeliveryPolymerizationVO> taskValue = taskMap.get(stepId);
        String taskId = taskValue.get(0).getTaskId();
        Date startTime = taskValue.get(0).getStartTime();
        Date endTime = taskValue.get(0).getEndTime();
        BigDecimal quantity = taskValue.get(0).getQuantity().setScale(0, RoundingMode.HALF_UP);
        BigDecimal finishedSumQty = taskValue.stream().map(DynamicDeliveryPolymerizationVO::getFinishedQuantity)
                .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, RoundingMode.HALF_UP);
        String operationColor = getOperationColor(startTime, quantity, finishedSumQty);
        DeliveryTrackingResultVO.DeliveryTrackingBody deliveryTrackingBody = DeliveryTrackingResultVO.DeliveryTrackingBody.builder()
                .taskId(taskId)
                .operationName(standardStepName)
                .productionTime(DateUtils.dateToString(startTime, DateUtils.COMMON_DATE_STR5) + "~"
                        + DateUtils.dateToString(endTime, DateUtils.COMMON_DATE_STR5))
                .count(finishedSumQty + "/" + quantity)
                .color(operationColor)
                .build();
        if (aFalse) {
            deliveryTrackingBody.setProductionTime(DateUtils.dateToString(endTime, DateUtils.COMMON_DATE_STR5));
        }
        return deliveryTrackingBody;
    }

    private void addDynamicWarehouseTime(List<DeliveryTrackingResultVO.DeliveryTrackingBody> dynamicDeliveryTrackingBody,
                                         Date deliveryTime,
                                         String warehousedOperation,
                                         Map<String, List<DynamicDeliveryPolymerizationVO>> taskMap) {
        // 工序包含进仓工序
        if (taskMap.containsKey(warehousedOperation)) {
            DeliveryTrackingResultVO.DeliveryTrackingBody deliveryBody = getDeliveryBody(taskMap, warehousedOperation, "进仓时间", Boolean.TRUE);
            dynamicDeliveryTrackingBody.add(deliveryBody);
        }
        DeliveryTrackingResultVO.DeliveryTrackingBody deliveryTimeTrackingBody = DeliveryTrackingResultVO.DeliveryTrackingBody.builder()
                .taskId("-1")
                .operationName("发货时间")
                .productionTime(DateUtils.dateToString(deliveryTime, DateUtils.COMMON_DATE_STR5))
                .build();
        dynamicDeliveryTrackingBody.add(deliveryTimeTrackingBody);
    }

    /**
     * color
     *
     * @param startTime      开始时间
     * @param quantity       总数量
     * @param finishedSumQty 完工数量
     * @return color
     */
    private String getOperationColor(Date startTime, BigDecimal quantity, BigDecimal finishedSumQty) {
        if (finishedSumQty.compareTo(quantity) >= 0) {
            return "green";
        }
        if (finishedSumQty.compareTo(BigDecimal.ZERO) > 0) {
            return "yellow";
        }
        if (finishedSumQty.signum() == 0 && null != startTime) {
            Date fiveMinutesBefore = DateUtil.offsetMinute(startTime, -5);
            if (new Date().after(fiveMinutesBefore)) {
                return "red";
            }
        }
        return null;
    }

    private void assemblyParam(DeliveryTrackingViewReq deliveryTrackingViewReq) {
        String standardOperation = deliveryTrackingViewReq.getStandardOperation();
        if (StrUtil.isNotEmpty(standardOperation)) {
            List<String> trackingIds = dynamicDeliveryTrackingTaskDao.selectTrackingIds(standardOperation);
            deliveryTrackingViewReq.setTrackingIds(trackingIds);
        }
    }

    @Override
    public List<DynamicDeliveryTrackingTaskVO> selectTaskGroupByPhysicalResourceId(String physicalResourceId) {
        return dynamicDeliveryTrackingTaskDao.selectTaskGroupByPhysicalResourceId(physicalResourceId);
    }

    @Override
    public List<DynamicDeliveryTrackingTaskVO> selectSubTaskByPhysicalResourceId(String physicalResourceId,
                                                                                 String taskId) {
        return dynamicDeliveryTrackingTaskDao.selectSubTaskByPhysicalResourceId(physicalResourceId, taskId);
    }

}
