package com.yhl.scp.mps.dynamicDeliveryTracking.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingStepResourceDO;
import com.yhl.scp.mps.dynamicDeliveryTracking.convertor.DynamicDeliveryTrackingSubTaskConvertor;
import com.yhl.scp.mps.dynamicDeliveryTracking.domain.entity.DynamicDeliveryTrackingSubTaskDO;
import com.yhl.scp.mps.dynamicDeliveryTracking.domain.service.DynamicDeliveryTrackingSubTaskDomainService;
import com.yhl.scp.mps.dynamicDeliveryTracking.dto.DynamicDeliveryTrackingSubTaskDTO;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.dao.DynamicDeliveryTrackingDao;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.dao.DynamicDeliveryTrackingReportDao;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.dao.DynamicDeliveryTrackingSubTaskDao;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.dao.DynamicDeliveryTrackingTaskDao;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po.DynamicDeliveryTrackingPO;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po.DynamicDeliveryTrackingReportPO;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po.DynamicDeliveryTrackingSubTaskPO;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po.DynamicDeliveryTrackingTaskPO;
import com.yhl.scp.mps.dynamicDeliveryTracking.service.DynamicDeliveryAlertingService;
import com.yhl.scp.mps.dynamicDeliveryTracking.service.DynamicDeliveryTrackingSubTaskService;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingSubTaskVO;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingSubTaskVXToVO;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingTaskVO;
import com.yhl.scp.mps.enums.ObjectTypeEnum;
import com.yhl.scp.mps.enums.TrackingStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>DynamicDeliveryTrackingSubTaskServiceImpl</code>
 * <p>
 * 动态交付跟踪工序任务明细表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 13:47:44
 */
@Slf4j
@Service
public class DynamicDeliveryTrackingSubTaskServiceImpl extends AbstractService implements DynamicDeliveryTrackingSubTaskService {

    @Resource
    private DynamicDeliveryTrackingSubTaskDao dynamicDeliveryTrackingSubTaskDao;

    @Resource
    private DynamicDeliveryTrackingSubTaskDomainService dynamicDeliveryTrackingSubTaskDomainService;

    @Resource
    private DynamicDeliveryTrackingReportDao dynamicDeliveryTrackingReportDao;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private DynamicDeliveryTrackingTaskDao dynamicDeliveryTrackingTaskDao;
    @Resource
    private DynamicDeliveryTrackingDao dynamicDeliveryTrackingDao;

    @Resource
    private DynamicDeliveryAlertingService dynamicDeliveryAlertingService;

    @Override
    public BaseResponse<Void> doCreate(DynamicDeliveryTrackingSubTaskDTO dynamicDeliveryTrackingSubTaskDTO) {
        // 0.数据转换
        DynamicDeliveryTrackingSubTaskDO dynamicDeliveryTrackingSubTaskDO = DynamicDeliveryTrackingSubTaskConvertor.INSTANCE.dto2Do(dynamicDeliveryTrackingSubTaskDTO);
        DynamicDeliveryTrackingSubTaskPO dynamicDeliveryTrackingSubTaskPO = DynamicDeliveryTrackingSubTaskConvertor.INSTANCE.dto2Po(dynamicDeliveryTrackingSubTaskDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        dynamicDeliveryTrackingSubTaskDomainService.validation(dynamicDeliveryTrackingSubTaskDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(dynamicDeliveryTrackingSubTaskPO);
        dynamicDeliveryTrackingSubTaskDao.insert(dynamicDeliveryTrackingSubTaskPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(DynamicDeliveryTrackingSubTaskDTO dynamicDeliveryTrackingSubTaskDTO) {
        // 0.数据转换
        DynamicDeliveryTrackingSubTaskDO dynamicDeliveryTrackingSubTaskDO = DynamicDeliveryTrackingSubTaskConvertor.INSTANCE.dto2Do(dynamicDeliveryTrackingSubTaskDTO);
        DynamicDeliveryTrackingSubTaskPO dynamicDeliveryTrackingSubTaskPO = DynamicDeliveryTrackingSubTaskConvertor.INSTANCE.dto2Po(dynamicDeliveryTrackingSubTaskDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        dynamicDeliveryTrackingSubTaskDomainService.validation(dynamicDeliveryTrackingSubTaskDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(dynamicDeliveryTrackingSubTaskPO);
        dynamicDeliveryTrackingSubTaskDao.update(dynamicDeliveryTrackingSubTaskPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<DynamicDeliveryTrackingSubTaskDTO> list) {
        List<DynamicDeliveryTrackingSubTaskPO> newList = DynamicDeliveryTrackingSubTaskConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        dynamicDeliveryTrackingSubTaskDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<DynamicDeliveryTrackingSubTaskDTO> list) {
        List<DynamicDeliveryTrackingSubTaskPO> newList = DynamicDeliveryTrackingSubTaskConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        dynamicDeliveryTrackingSubTaskDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return dynamicDeliveryTrackingSubTaskDao.deleteBatch(idList);
        }
        return dynamicDeliveryTrackingSubTaskDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public DynamicDeliveryTrackingSubTaskVO selectByPrimaryKey(String id) {
        DynamicDeliveryTrackingSubTaskPO po = dynamicDeliveryTrackingSubTaskDao.selectByPrimaryKey(id);
        return DynamicDeliveryTrackingSubTaskConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "DYNAMIC_DELIVERY_TRACKING_SUB_TASK")
    public List<DynamicDeliveryTrackingSubTaskVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "DYNAMIC_DELIVERY_TRACKING_SUB_TASK")
    public List<DynamicDeliveryTrackingSubTaskVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<DynamicDeliveryTrackingSubTaskVO> dataList = dynamicDeliveryTrackingSubTaskDao.selectByCondition(sortParam, queryCriteriaParam);
        DynamicDeliveryTrackingSubTaskServiceImpl target = SpringBeanUtils.getBean(DynamicDeliveryTrackingSubTaskServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<DynamicDeliveryTrackingSubTaskVO> selectByParams(Map<String, Object> params) {
        List<DynamicDeliveryTrackingSubTaskPO> list = dynamicDeliveryTrackingSubTaskDao.selectByParams(params);
        return DynamicDeliveryTrackingSubTaskConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<DynamicDeliveryTrackingSubTaskVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.DYNAMIC_DELIVERY_TRACKING_SUB_TASK.getCode();
    }

    @Override
    public List<DynamicDeliveryTrackingSubTaskVO> invocation(List<DynamicDeliveryTrackingSubTaskVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public List<DynamicDeliveryTrackingSubTaskVO> selectSubTaskByTaskId(String taskId, Boolean limitFlag) {
        if (StringUtils.isBlank(taskId)) {
            return new ArrayList<>();
        }
        List<DynamicDeliveryTrackingSubTaskVO> subTaskVOS = dynamicDeliveryTrackingSubTaskDao.selectSubTaskByTaskId(taskId);
        if (CollectionUtils.isEmpty(subTaskVOS)) {
            return new ArrayList<>();
        }
        if (limitFlag) {
            Date now = new Date();
            List<DynamicDeliveryTrackingSubTaskVO> before = new ArrayList<>();
            List<DynamicDeliveryTrackingSubTaskVO> after = new ArrayList<>();
            DynamicDeliveryTrackingSubTaskVO curr = null;
            for (DynamicDeliveryTrackingSubTaskVO subTaskVO : subTaskVOS) {
                long start = subTaskVO.getStartTime().getTime();
                long end = subTaskVO.getEndTime().getTime();
                if (now.getTime() >= start && now.getTime() <= end) {
                    curr = subTaskVO;
                } else if (now.getTime() < start) {
                    after.add(subTaskVO);
                } else {
                    before.add(subTaskVO);
                }
            }
            List<DynamicDeliveryTrackingSubTaskVO> newList = new ArrayList<>();
            // 只返回三条数据
            int size = 3;
            if (curr != null) {
                newList.add(curr);
                size--;
            }

            if (CollectionUtils.isNotEmpty(before) || CollectionUtils.isNotEmpty(after)) {
                while (size > 0) {
                    if (CollectionUtils.isNotEmpty(before)) {
                        newList.add(before.get(0));
                        before.remove(0);
                        size--;
                    }
                    if (CollectionUtils.isNotEmpty(after)) {
                        newList.add(after.get(0));
                        after.remove(0);
                        size--;
                    }
                    if (CollectionUtils.isEmpty(before) && CollectionUtils.isEmpty(after)) {
                        break;
                    }
                }
            }
            newList = newList.stream().sorted((Comparator.comparing(DynamicDeliveryTrackingSubTaskVO::getStartTime))).collect(Collectors.toList());
            return newList;
        } else {
            return subTaskVOS.stream().sorted((Comparator.comparing(DynamicDeliveryTrackingSubTaskVO::getStartTime))).collect(Collectors.toList());
        }
    }

    @Override
    public List<DynamicDeliveryTrackingSubTaskVO> buildSubTaskByTask(List<DynamicDeliveryTrackingTaskVO> taskList) {
        if (CollectionUtils.isEmpty(taskList)) {
            return Collections.emptyList();
        }
        // todo 缺少参数校验
        List<DynamicDeliveryTrackingSubTaskVO> result = new ArrayList<>();
        List<String> taskIdList = taskList.stream().map(BaseVO::getId).filter(Objects::nonNull).collect(Collectors.toList());
        // 之前已经下发过了，有明细数据
        Map<String, List<DynamicDeliveryTrackingSubTaskVO>> existSubMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(taskIdList)) {
            List<DynamicDeliveryTrackingSubTaskPO> taskPOS = dynamicDeliveryTrackingSubTaskDao.selectByParams(ImmutableMap.of("taskIdList", taskIdList));
            List<DynamicDeliveryTrackingSubTaskVO> subTaskVOS = DynamicDeliveryTrackingSubTaskConvertor.INSTANCE.po2Vos(taskPOS);
            existSubMap = subTaskVOS.stream().collect(Collectors.groupingBy(DynamicDeliveryTrackingSubTaskVO::getTaskId));
        }
        for (DynamicDeliveryTrackingTaskVO taskVO : taskList) {
            if (existSubMap.containsKey(taskVO.getId())) {
                List<DynamicDeliveryTrackingSubTaskVO> taskVOS = existSubMap.get(taskVO.getId());
                result.addAll(taskVOS);
            } else {
                result.addAll(generateSubTask(taskVO));
            }

        }
        return result;
    }

    @Override
    public List<DynamicDeliveryTrackingSubTaskVO> generateSubTask(DynamicDeliveryTrackingTaskVO taskVO) {
        List<DynamicDeliveryTrackingSubTaskVO> result = new ArrayList<>();
        String physicalResourceId = taskVO.getPhysicalResourceId();
        RoutingStepResourceDO routingStepResourceDO = taskVO.getRoutingStepResourceDOList().stream().filter(t -> physicalResourceId.equals(t.getPhysicalResourceId())).findFirst().orElse(null);
        if (routingStepResourceDO != null) {
            // 节拍
            BigDecimal unitProductionTime = routingStepResourceDO.getUnitProductionTime();
            Date startTime = taskVO.getStartTime();
            BigDecimal quantity = taskVO.getQuantity();
            // 制造时长
            BigDecimal cost = quantity.multiply(unitProductionTime);
            // 制造时长超一小时需要进行分割
            if (cost.intValue() > 3600) {
                while (cost.intValue() > 3600) {
                    BigDecimal decimal = new BigDecimal(3600);
                    DynamicDeliveryTrackingSubTaskVO subTaskVO = new DynamicDeliveryTrackingSubTaskVO();
                    subTaskVO.setTaskId(taskVO.getId());
                    subTaskVO.setStartTime(startTime);
                    Date endTime = DateUtils.moveHour(startTime, 1);
                    subTaskVO.setEndTime(endTime);
                    startTime = endTime;
                    cost = cost.subtract(decimal);
                    BigDecimal qty = decimal.divide(unitProductionTime, 0, RoundingMode.HALF_UP);
                    subTaskVO.setPlannedQuantity(qty);
                    subTaskVO.setFinishedQuantity(BigDecimal.ZERO);
                    quantity = quantity.subtract(qty);
                    result.add(subTaskVO);
                }
            }
            // 制造时长不足一小时不需要进行分割
            if (cost.intValue() > 0) {
                DynamicDeliveryTrackingSubTaskVO subTaskVO = new DynamicDeliveryTrackingSubTaskVO();
                subTaskVO.setTaskId(taskVO.getId());
                subTaskVO.setStartTime(startTime);
                subTaskVO.setEndTime(DateUtils.moveCalendar(startTime, Calendar.SECOND, cost.intValue()));
                cost = BigDecimal.ZERO;
                subTaskVO.setPlannedQuantity(quantity);
                subTaskVO.setFinishedQuantity(BigDecimal.ZERO);
                result.add(subTaskVO);
            }
        }
        return result;
    }

    @Override
    public BaseResponse<DynamicDeliveryTrackingSubTaskVXToVO> search(String id) {
        // 获取本条数据
        DynamicDeliveryTrackingSubTaskVO currentVO = this.selectByPrimaryKey(id);
        if (Objects.isNull(currentVO)) {
            log.error("通过该任务工序id{}没有匹配到数据", id);
            return BaseResponse.error("通过该任务工序id没有匹配到数据");
        }
        // 获取task的ID
        String taskId = currentVO.getTaskId();
        List<DynamicDeliveryTrackingSubTaskVO> dynamicDeliveryTrackingSubTaskVOS = this.selectSubTaskByTaskId(taskId, Boolean.FALSE);
        // 已报工数量
        int sum = dynamicDeliveryTrackingSubTaskVOS.stream().map(DynamicDeliveryTrackingSubTaskVO::getFinishedQuantity).reduce(BigDecimal.ZERO, BigDecimal::add).intValue();
        DynamicDeliveryTrackingSubTaskVXToVO vxVO = dynamicDeliveryTrackingSubTaskDao.selectBySubTaskId(id);
        if (Objects.isNull(vxVO)) {
            log.error("通过该任务工序id{}没有匹配到数据", id);
            return BaseResponse.error("通过该任务工序没有匹配到数据");
        } else {
            if (StringUtils.isEmpty(vxVO.getPhysicalResourceCode())) {
                log.error("该任务工序id{}没有到产线资源，请联系管理员进行配置", id);
                return BaseResponse.error("该任务工序没有到产线资源，请联系管理员进行配置");
            } else {
                if (StringUtils.isEmpty(vxVO.getProductionPlanner())) {
                    log.error("该任务工序对应的产线{}没有维护计划员，请联系管理员进行配置", vxVO.getPhysicalResourceCode());
                    return BaseResponse.error("该任务工序对应的产线" + vxVO.getPhysicalResourceCode() + "没有维护计划员，请联系管理员进行配置");
                } else {
                    String[] ids = vxVO.getProductionPlanner().split(",");
                    List<String> idList = Arrays.asList(ids);
                    // 获取所有人员
                    List<User> users = ipsNewFeign.userList();
                    String productPlanners = users.stream().filter(item -> idList.contains(item.getId())).collect(Collectors.toList()).stream().map(item -> item.getCnName() + "(" + item.getUserName() + ")").collect(Collectors.joining(", "));
                    vxVO.setProductionPlanner(productPlanners);
                }
            }
            vxVO.setHistoryQuantity(sum);
            return BaseResponse.success(BaseResponse.OP_SUCCESS, vxVO);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse<Void> saveVXData(DynamicDeliveryTrackingSubTaskVXToVO vxToVO) {
        try {
            DynamicDeliveryTrackingSubTaskVO currentVO = this.selectByPrimaryKey(vxToVO.getId());
            // 完工数量赋值
            BigDecimal total = currentVO.getFinishedQuantity().add(new BigDecimal(vxToVO.getFinishedQuantity()));
            currentVO.setFinishedQuantity(total);
            DynamicDeliveryTrackingSubTaskPO po = new DynamicDeliveryTrackingSubTaskPO();
            BeanUtils.copyProperties(currentVO, po);

            // 新增操作记录
            User user = ipsNewFeign.getUserByUserName(vxToVO.getUserName());
            Date date = new Date();

            DynamicDeliveryTrackingReportPO reportPO = new DynamicDeliveryTrackingReportPO();
            reportPO.setId(UUIDUtil.getUUID());
            reportPO.setSubTaskId(vxToVO.getId());
            reportPO.setReportingQuantity(new BigDecimal(vxToVO.getFinishedQuantity()));
            reportPO.setUserId(vxToVO.getUserName());
            reportPO.setEnabled(YesOrNoEnum.YES.getCode());
            po.setModifyTime(date);
            po.setModifier(vxToVO.getUserName());
            if (!Objects.isNull(user)) {
                reportPO.setCreator(user.getId());
                reportPO.setModifier(user.getId());
                po.setModifier(user.getId());
            }
            reportPO.setCreateTime(date);
            reportPO.setModifyTime(date);
            dynamicDeliveryTrackingReportDao.insert(reportPO);

            // 更新sub_task数据
            dynamicDeliveryTrackingSubTaskDao.update(po);

            String taskId = po.getTaskId();
            DynamicDeliveryTrackingTaskPO taskPO = dynamicDeliveryTrackingTaskDao.selectByPrimaryKey(taskId);
            // 这里查询汇总了每个工序的已报工数量，用以判断整个追踪任务是否已完成
            List<DynamicDeliveryTrackingSubTaskVO> sumSubTaskVOS = dynamicDeliveryTrackingSubTaskDao.selectSumSubTaskByTrackingId(taskPO.getDynamicDeliveryTrackingId());
            boolean finishFlag = true;
            for (DynamicDeliveryTrackingSubTaskVO sumSubTaskVO : sumSubTaskVOS) {
                if (sumSubTaskVO.getFinishedQuantity().compareTo(sumSubTaskVO.getPlannedQuantity()) < 0) {
                    finishFlag = false;
                    break;
                }
            }
            if (finishFlag) {
                DynamicDeliveryTrackingPO trackingPO = dynamicDeliveryTrackingDao.selectByPrimaryKey(taskPO.getDynamicDeliveryTrackingId());
                trackingPO.setTrackingStatus(TrackingStatusEnum.FINISHED.getCode());
                BasePOUtils.updateFiller(trackingPO);
                dynamicDeliveryTrackingDao.update(trackingPO);
            } else {
                DynamicDeliveryTrackingPO trackingPO = dynamicDeliveryTrackingDao.selectByPrimaryKey(taskPO.getDynamicDeliveryTrackingId());
                if (trackingPO != null && TrackingStatusEnum.PUBLISHED.getCode().equals(trackingPO.getTrackingStatus())) {
                    trackingPO.setTrackingStatus(TrackingStatusEnum.IN_EXECUTION.getCode());
                    BasePOUtils.updateFiller(trackingPO);
                    dynamicDeliveryTrackingDao.update(trackingPO);
                }
            }
            // 触发子任务完成事件
            dynamicDeliveryAlertingService.processWorkReport(vxToVO.getScenario(), vxToVO.getPhysicalResourceId(),
                    vxToVO.getPeriodKey(), currentVO);
            return BaseResponse.success(BaseResponse.OP_SUCCESS);
        } catch (Exception e) {
            log.error("保存动态交付数据时报错,{}", e.getMessage(), e.getCause());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly(); // 手动回滚
            return BaseResponse.error("保存动态交付数据时报错:" + e.getMessage());
        }
    }


    public static void paramsCheck(DynamicDeliveryTrackingSubTaskVO subTaskVO) {
        if (subTaskVO == null) {
            throw new BusinessException("参数不能为空！");
        }
        if (subTaskVO.getPlannedQuantity() == null || subTaskVO.getPlannedQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("计划数量必须大于0！");
        }
        if (subTaskVO.getStartTime() == null) {
            throw new BusinessException("生产开始时间不能为空！");
        }
        if (subTaskVO.getEndTime() == null) {
            throw new BusinessException("生产结束时间不能为空！");
        }
    }

}