package com.yhl.scp.mps.dispatch.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.JacksonUtils;
import com.yhl.scp.biz.common.enums.ModuleCodeEnum;
import com.yhl.scp.common.enums.AlgorithmLogStatusEnum;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.das.core.AlgorithmVersion;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.mds.basic.resource.enums.ResourceCategoryEnum;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mps.dispatch.output.RzzMpsAlgorithmOutput;
import com.yhl.scp.mps.dispatch.service.DispatchService;
import com.yhl.scp.mps.domain.dispatch.IMpsSchedule;
import com.yhl.scp.mps.manualAdjust.support.ManualAdjustSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName DispatchServiceImpl
 * @Description 算法实现
 * @Date 2024/9/10 16:25
 * <AUTHOR>
 * @Copyright 瑞之泽
 * @Version 1.0
 */
@Service
@Slf4j
public class DispatchServiceImpl implements DispatchService {

    @Value("${schedule.url}")
    private String port;

    @Value("${schedule.create}")
    private String createUri;

    @Value("${schedule.check}")
    private String checkUri;

    @Value("${schedule.result}")
    private String resultUri;

    @Resource
    private IpsFeign ipsFeign;

    @Resource
    private IMpsSchedule schedule;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private RedisUtil redisUtil;

    @Override
    public void doSchedule(AlgorithmLog algorithmLog) {
        schedule.doMpsSchedule(algorithmLog);
    }

    @Override
    public BaseResponse doCreate(String planTypeId, Map<String, List<String>> params) {
        String userId = SystemHolder.getUserId();
        AlgorithmLog algorithmLogMap = new AlgorithmLog();
        algorithmLogMap.setAlgorithmVersion(AlgorithmVersion.MPS2);
        algorithmLogMap.setCreator(userId);
        algorithmLogMap.setProductionPlanner(SystemHolder.getUserName());
        // 当前登录人负责的关键设备的权限组标准资源
        List<String> plannerStandardCode = newMdsFeign.getPlannerStandardResource(SystemHolder.getUserId());
        if (CollectionUtils.isEmpty(plannerStandardCode)) {
            return BaseResponse.error("无任何产线组权限，无法创建排产任务");
        }
        List<PhysicalResourceVO> plannerPhysicalResource = newMdsFeign.getPlannerPhysicalResource(SystemHolder.getUserId());
        if (CollectionUtils.isEmpty(plannerPhysicalResource)) {
            return BaseResponse.error("当前用户没有可用产线权限，无法创建排产任务");
        }
        List<String> physicalResourceList = plannerPhysicalResource.stream().filter(t -> ResourceCategoryEnum.MAIN.getCode().equals(t.getResourceCategory()) && YesOrNoEnum.NO.getCode().equals(t.getInfiniteCapacity())).map(PhysicalResourceVO::getPhysicalResourceCode).collect(Collectors.toList());
        String plannerStandardCodeStr;
        // 选择的产线组
        List<String> lineGroupList = params.getOrDefault("lineGroup", new ArrayList<>());
        if (CollectionUtils.isNotEmpty(lineGroupList)) {
            if (lineGroupList.contains("*")) {
                plannerStandardCodeStr = StringUtils.join(plannerStandardCode, ",");
            } else {
                plannerStandardCodeStr = StringUtils.join(lineGroupList, ",");
            }
        } else {
            plannerStandardCodeStr = StringUtils.join(plannerStandardCode, ",");
        }
        algorithmLogMap.setLineGroup(plannerStandardCodeStr);
        List<CollectionValueVO> yztResource = ipsFeign.getByCollectionCode("YZT_RESOURCE");
        // 压制台主资源代码
        List<String> yztResourceList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(yztResource)) {
            yztResourceList.addAll(yztResource.stream().map(CollectionValueVO::getCollectionValue).collect(Collectors.toList()));
        }
        // 选择的产线
        String plannerPhysicalCodeStr;
        List<String> allResourceList = new ArrayList<>();
        List<String> productLineList = params.getOrDefault("productLine", new ArrayList<>());
        if (CollectionUtils.isNotEmpty(productLineList)) {
            if (productLineList.contains("*")) {
                allResourceList = physicalResourceList;
            } else {
                allResourceList = productLineList;
            }
        } else {
            allResourceList = physicalResourceList;
        }
        List<String> collect = allResourceList.stream().filter(yztResourceList::contains).collect(Collectors.toList());
        // 让使用压制台的主资源强制绑定一起跑
        if (CollectionUtils.isNotEmpty(collect)) {
            // 选择的产线存在要用压制台的资源
            allResourceList.addAll(yztResourceList);
        }
        allResourceList = allResourceList.stream().distinct().collect(Collectors.toList());
        plannerPhysicalCodeStr = StringUtils.join(allResourceList, ",");
        algorithmLogMap.setProductLine(plannerPhysicalCodeStr);

        // 获取其他在运行的产线组资源
        List<AlgorithmLog> algorithmLogs = ipsFeign.selectTaskIsNotFail(Collections.singletonList(ModuleCodeEnum.MPS.getCode()));
        List<AlgorithmLog> userRunningLog = algorithmLogs.stream().filter(p -> p.getCreator().equals(userId)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userRunningLog)) {
            return BaseResponse.error("您已有排产任务在运行，请勿重复运行");
        }
        BaseResponse<Void> checkResponse = checkAdjustIsRunning(plannerPhysicalResource, allResourceList);
        if (!checkResponse.getSuccess()) {
            return checkResponse;
        }
        if (CollectionUtils.isNotEmpty(algorithmLogs)) {
            List<String> runningLineGroup = new ArrayList<>();
            for (AlgorithmLog algorithmLog : algorithmLogs) {
                String lineGroup = algorithmLog.getProductLine();
                if (StrUtil.isNotEmpty(lineGroup)) {
                    runningLineGroup.addAll(Arrays.asList(lineGroup.split(",")));
                }
            }
            // 判断关键产线组是否存在交集，如果有则不允许创建排产任务
            List<String> physicalList = Arrays.asList(plannerPhysicalCodeStr.split(","));
            List<String> interSection = CollectionUtils.getInterSection(physicalList, runningLineGroup.stream().distinct().collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(interSection)) {
                return BaseResponse.error("当前产线（" + interSection + "）正在运行任务，请稍后再试！");
            }
        }
        algorithmLogMap.setRemark(JSONUtil.toJsonStr(plannerStandardCode));
        BaseResponse res = ipsFeign.createAlgorithmLogNew(algorithmLogMap);
        if (res.getSuccess().equals(Boolean.FALSE)) {
            log.error("主生产计划创建算法日志终止：{}", JSONUtil.toJsonStr(res));
            return res;
        }
        String logId = res.getData().toString();
        return BaseResponse.success("您的生产计划排产任务创建成功，请等待", logId);
    }

    /**
     * 校验是否有手动拖拽正在执行
     *
     * @param plannerPhysicalResource
     * @param allResourceList
     * @return
     */
    private BaseResponse<Void> checkAdjustIsRunning(List<PhysicalResourceVO> plannerPhysicalResource, List<String> allResourceList) {
        if (CollectionUtils.isEmpty(plannerPhysicalResource) || CollectionUtils.isEmpty(allResourceList)) {
            return BaseResponse.success();
        }
        Map<String, String> physicalResourceMap = plannerPhysicalResource.stream().collect(Collectors.toMap(PhysicalResourceVO::getPhysicalResourceCode, PhysicalResourceVO::getId, (v1, v2) -> v1));
        boolean checkResult = true;
        for (String resourceCode : allResourceList) {
            String resourceId = physicalResourceMap.get(resourceCode);
            if (StringUtils.isBlank(resourceId)) {
                continue;
            }
            String targetResourceKey = String.format(ManualAdjustSupport.RESOURCE_LOCK_KEY_PREFIX, resourceId);
            if (redisUtil.hasKey(targetResourceKey)) {
                checkResult = false;
                break;
            }
        }
        if (!checkResult) {
            return BaseResponse.error("产线正在运行排产任务，请稍后再试");
        }
        return BaseResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public BaseResponse resultAnalysis(AlgorithmLog algorithmLog) throws UnsupportedEncodingException {
        log.info("调用算法服务获取算法结果，ScheduleServiceImpl.doResultAnalysis, executionNumber is " + algorithmLog.getExecutionNumber());
        String moduleCode = SystemModuleEnum.MPS.getCode();
        // 如果参数值中带&，需要转义处理
        String url = getBaseUrl(algorithmLog) + resultUri + "?executionNumber=" + algorithmLog.getExecutionNumber() + "&moduleCode=" + URLEncoder.encode(moduleCode, "UTF-8");
        if (StringUtils.isNotBlank(algorithmLog.getAlgorithmVersion())) {
            url = url + "&algorithmVersion=" + algorithmLog.getAlgorithmVersion();
        }
        BaseResponse baseResponse = restTemplate.postForObject(url, null, BaseResponse.class);
        Assert.isTrue(Objects.nonNull(baseResponse), "算法结果执行为null");
        log.info("获取算法结果：{}", baseResponse.getSuccess());
        if (!baseResponse.getSuccess()) {
            log.error("算法结果解析失败，das返回失败");
            return BaseResponse.error("算法结果解析失败");
        }
        String data = JacksonUtils.toJson(baseResponse.getData());
        log.info("开始进行Jackson");
        RzzMpsAlgorithmOutput mpsAlgorithmOutput = JacksonUtils.toObj(data, new TypeReference<RzzMpsAlgorithmOutput>() {
        });
        log.info("结束Jackson");
        schedule.doAnalysisAlgorithmOutputData(mpsAlgorithmOutput, algorithmLog);
        return BaseResponse.success("解析MPS算法结果成功");
    }

    @Override
    public BaseResponse getAlgorithmStatus(AlgorithmLog algorithmLog) throws UnsupportedEncodingException {
        String moduleCode = algorithmLog.getModuleCode();
        // 如果参数值中带&，需要转义处理

        BaseResponse baseResponse = restTemplate.postForObject(getBaseUrl(algorithmLog) + checkUri + "?executionNumber=" + algorithmLog.getExecutionNumber() + "&" + "moduleCode=" + URLEncoder.encode(moduleCode, "UTF-8"), null, BaseResponse.class);
        String executorStatus = String.valueOf(baseResponse.getData());
        if (AlgorithmLogStatusEnum.FAIL.getCode().equals(executorStatus)) {
            log.warn("algorithm run failed, executionNumber is " + algorithmLog.getExecutionNumber());
        }
        return baseResponse;
    }

    /**
     * MPS算法结束后的前处理操作
     *
     * @param rzzMpsAlgorithmOutput
     * @return
     */
    @Override
    public RzzMpsAlgorithmOutput handleDataBeforeAnalysis(RzzMpsAlgorithmOutput rzzMpsAlgorithmOutput) {
        RzzMpsAlgorithmOutput output = new RzzMpsAlgorithmOutput();
        return output;
    }

    private String getBaseUrl(AlgorithmLog algorithmLog) {
        return "http://" + algorithmLog.getIpAddress() + ":" + port;
    }

}
