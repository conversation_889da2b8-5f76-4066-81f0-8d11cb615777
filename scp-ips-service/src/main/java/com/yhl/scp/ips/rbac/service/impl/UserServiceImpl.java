package com.yhl.scp.ips.rbac.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableMap;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.AesEncryptUtil;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.common.enums.UserTypeEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.NormalEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.rbac.dao.DeptDao;
import com.yhl.scp.ips.rbac.dao.DeptUserDao;
import com.yhl.scp.ips.rbac.dao.UserDao;
import com.yhl.scp.ips.rbac.dao.UserRoleDao;
import com.yhl.scp.ips.rbac.dto.UserDTO;
import com.yhl.scp.ips.rbac.entity.*;
import com.yhl.scp.ips.rbac.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>UserServiceImpl</code>
 * <p>
 * UserServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-07-17 20:21:06
 */
@Slf4j
@Service(value = "userService")
public class UserServiceImpl implements UserService {

    @Autowired
    private UserDao userDao;

    @Autowired
    private UserRoleDao userRoleDao;

    @Autowired
    private DeptDao deptDao;

    @Autowired
    private DeptUserDao deptUserDao;

    @Autowired
    private TenantUserService tenantUserService;

    @Autowired
    private DeptUserService deptUserService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private DeptService deptService;

    @Autowired
    private NewDcpFeign newDcpFeign;

    @Override
    public BaseResponse doCreate(User user) {
        User checkUser = getUserByUserName(user.getUserName());
        if (null != checkUser) {
            return new BaseResponse(false, "创建用户失败，用户名 " + user.getUserName() + " 已存在!");
        }
        String id = UUIDUtil.getUUID();
        // 超级管理员只创建租户管理员
        if (UserTypeEnum.SYSTEM_ADMIN.getCode().equals(SystemHolder.getUserType())) {
            user.setUserType(UserTypeEnum.TENANT_ADMIN.getCode());
            List<Role> roles = roleService.selectByIds(user.getRoleIds());
            List<String> tenantIds = roles.stream().map(Role::getTenantId).distinct().collect(Collectors.toList());
            for (String tenantId : tenantIds) {
                tenantUserService.doCreate(id, tenantId, UserTypeEnum.TENANT_ADMIN.getCode());
                // 部门和用户关系
                Dept tenantTopDept = deptService.selectRootDeptByTenantId(tenantId);
                deptUserService.doCreate(id, tenantTopDept.getId());
            }
        } else {
            tenantUserService.doCreate(id, SystemHolder.getTenantId(), user.getUserType());

            // 部门和用户关系
            deptUserService.doCreate(id, user.getDeptId());
        }
        // 所有用户新增密码默认为：123456
        String password = AesEncryptUtil.encrypt("123456");
        user.setId(id);
        user.setPassword(password);
        user.setCreateTime(new Date());
        user.setCreator(SystemHolder.getUserId());
        user.setEnabled(YesOrNoEnum.YES.name());
        userDao.insert(user);

        if (!CollectionUtils.isEmpty(user.getRoleIds())) {
            for (String roleId : user.getRoleIds()) {
                UserRole userRole = new UserRole();
                userRole.setUserId(user.getId());
                userRole.setRoleId(roleId);
                userRoleDao.insert(userRole);
            }
        }
        return new BaseResponse(true, "用户创建成功");
    }


    @Override
    public BaseResponse doUpdate(User user) {
        User checkUser = userDao.selectByPrimaryKey(user.getId());
        checkUser.setCnName(user.getCnName());
        checkUser.setUserType(user.getUserType());
        checkUser.setEmail(user.getEmail());
        checkUser.setPhone(user.getPhone());
        checkUser.setStaffCode(user.getStaffCode());
        checkUser.setUserName(user.getUserName());
        if (UserTypeEnum.SYSTEM_ADMIN.getCode().equals(SystemHolder.getUserType())) {
            user.setUserType(UserTypeEnum.TENANT_ADMIN.getCode());
            List<Role> roles = roleService.selectByIds(user.getRoleIds());
            List<String> tenantIds = roles.stream().map(Role::getTenantId).distinct().collect(Collectors.toList());
//            deptUserService.doDeleteByUserId(user.getId());
            tenantUserService.doDeleteTenantAdminByUserId(user.getId());
            userRoleDao.deleteByUserId(user.getId());
            for (String tenantId : tenantIds) {
                tenantUserService.doCreate(user.getId(), tenantId, UserTypeEnum.TENANT_ADMIN.getCode());
                // 部门和用户关系
//                Dept tenantTopDept = deptService.selectRootDeptByTenantId(tenantId);
//                deptUserService.doCreate(user.getId(), tenantTopDept.getId());
            }
        } else {
            tenantUserService.doDeleteByUserIdAndTenantId(user.getId(), SystemHolder.getTenantId());
            tenantUserService.doCreate(user.getId(), SystemHolder.getTenantId(), user.getUserType());

            // 部门和用户关系
//            deptUserService.doDeleteByUserIdAndTenantId(user.getId(), SystemHolder.getTenantId());
//            deptUserService.doCreate(user.getId(), user.getDeptId());

            userRoleDao.deleteByUserIdAndTenantId(user.getId(), SystemHolder.getTenantId());
        }
        userDao.updateByPrimaryKey(checkUser);

        if (!CollectionUtils.isEmpty(user.getRoleIds())) {
            for (String roleId : user.getRoleIds()) {
                UserRole userRole = new UserRole();
                userRole.setUserId(user.getId());
                userRole.setRoleId(roleId);
                userRoleDao.insert(userRole);
            }
        }
        return new BaseResponse(true, "更新成功");
    }

    @Override
    public User getUserByUserName(String userName) {
        return userDao.selectByUserName(userName);
    }

    @Override
    public void doDelete(String userId) {
        userDao.deleteUserRelative(userId);
    }

    @Override
    public void doDisable(String userId) {
        User user = userDao.selectByPrimaryKey(userId);
        user.setEnabled(YesOrNoEnum.NO.getCode());
        user.setModifyTime(new Date());
        user.setModifier(SystemHolder.getUserId());
        userDao.updateByPrimaryKey(user);
    }

    @Override
    public void doEnable(String userId) {
        User user = userDao.selectByPrimaryKey(userId);
        user.setEnabled(YesOrNoEnum.YES.getCode());
        user.setModifyTime(new Date());
        user.setModifier(SystemHolder.getUserId());
        userDao.updateByPrimaryKey(user);
    }

    @Override
    public List<User> selectPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        List<User> users = userDao.selectByPage(sortParam, queryCriteriaParam, SystemHolder.getTenantId());
        for (User user : users) {
            List<Role> roles = roleService.selectByUserId(user.getId());
            user.setRolesName(roles.stream().map(Role::getRoleName).collect(Collectors.toList()));
        }
        return users;
    }

    @Override
    public List<User> selectByParams(Map<String, Object> params) {
        return userDao.selectByParams(params);
    }

    @Override
    public User selectById(String id) {
        User user = userDao.selectByPrimaryKey(id);
        List<Role> roles = roleService.selectByUserId(id);
        List<String> roleIds = roles.stream().map(Role::getId).collect(Collectors.toList());
        List<Dept> depts = deptService.selectByCurrentUserId(id);
        if (!depts.isEmpty()){
            user.setDeptName(depts.stream().map(Dept::getDeptName).collect(Collectors.joining(";")));
        }
        user.setRoleIds(roleIds);
        return user;
    }

    @Override
    public List<User> selectAll() {
        return userDao.selectAll();
    }

    @Override
    public void doUpdateById(User user) {
        userDao.updateByPrimaryKey(user);
    }

    /**
     * 定时startTime不设只
     * 界面
     *
     * @param startTime
     * @return
     */
    @Override
    public BaseResponse<Void> syncSyncUser(String startTime) {
        log.info("开始同步IAM用户数据");
        try {
            Long timeLong;
            HashMap<String, Object> map = MapUtil.newHashMap();
            // 获取当前时间  startTime为空表示定时
            if (!StringUtils.isBlank(startTime)) {
                timeLong = DateUtils.stringToDate(startTime, DateUtils.COMMON_DATE_STR1).getTime();
                map.put("triggerType", YesOrNoEnum.YES.getCode());
            } else {
                timeLong = 0L;
            }
            map.put("startTime", timeLong);

            newDcpFeign.callExternalApi(TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.IAM.getCode(),
                    ApiCategoryEnum.ALL_USER.getCode(), map);
        } catch (Exception e) {
            log.error("数据执行报错，{}", e.getMessage());
        }
        return BaseResponse.success("数据同步成功");
    }

    @Override
    public BaseResponse<String> syncUserData(List<UserDTO> list) {
        String operator = SystemHolder.getUserId() == null ? "-1" : SystemHolder.getUserId();
        Date date = new Date();
        // 查询用户表
        HashMap<String, Object> map = new HashMap<>(1);
        // 查询组织表
        HashMap<String, Object> map1 = MapUtil.newHashMap(1);
        // 查询组织用户关系映射表
        HashMap<String, Object> map2 = MapUtil.newHashMap(1);
        // 用户插入表
        List<User> insertUserList = new ArrayList<>();
        // 组织用户关系映射表
        List<DeptUser> insertDeptUserList = new ArrayList<>();
        // 用户角色表
        List<UserRole> userRoleList = ListUtil.list(false);
        // 租户用户表
        List<TenantUser> tenantUserList = ListUtil.list(false);
        // 设置同步IAM用户 普通用户（仅IAM默认）
        String normalRole = "普通用户（仅IAM默认）";
        List<Role> roles = roleService.selectByName(normalRole);
        if (roles.isEmpty()) {
            log.error("没有默认的IAM用户->{}", normalRole);
            return BaseResponse.error("没有默认的IAM用户->{}", normalRole);
        }
        Role role = roles.get(0);
        // 清除同一个用户进行多次操作
        Map<String, UserDTO> collect = list.stream().collect(Collectors.toMap(
                item -> item.getUserName(),
                item -> item,
                (item1, item2) -> item1.getRequestLogCreateTime().compareTo(item2.getRequestLogCreateTime()) > 0
                        ? item1 : item2
        ));
        Collection<UserDTO> values = collect.values();
        Set<String> userNames = collect.keySet();
        // 获取同步数据集合
        map.put("userNames", userNames);
        List<User> oldDataList = userDao.selectByParams(map);
        Map<String, User> oldDataMap = oldDataList.stream().collect(Collectors.toMap(
                item -> item.getUserName(),
                item -> item
        ));
        Set<String> oldDataUserNames = oldDataMap.keySet();
        // 数据遍历
        for (UserDTO syncDto : values) {
            User user = this.changeUser(syncDto);
            // 判断数据是新增还是修改
            if (oldDataUserNames.contains(syncDto.getUserName())) {
                // 更新用户 邮箱，手机，状态
                User oldUser = oldDataMap.get(syncDto.getUserName());
                oldUser.setModifier(operator);
                oldUser.setModifyTime(date);
                oldUser.setEmail(user.getEmail());
                oldUser.setPhone(user.getPhone());
                oldUser.setUserStatus(user.getUserStatus());
                if ("1".equals(user.getUserStatus()) || "3".equals(user.getUserStatus())) {
                    oldUser.setEnabled(YesOrNoEnum.YES.getCode());
                } else {
                    oldUser.setEnabled(YesOrNoEnum.NO.getCode());
                }
                userDao.updateByPrimaryKey(oldUser);
                // 根据用户查询映射表
                map2.put("userId", oldUser.getId());
                List<DeptUser> deptUserList = deptUserDao.selectByParams(map2);
                // 查询组织id
                map1.put("deptCode", syncDto.getOrgs().get("idt_org__org_code"));
                List<Dept> oldDeptList = deptDao.selectByParams(map1);
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(oldDeptList)) {
                    log.error("bpim组织表中没有找到组织编码deptCode为{}的数据", syncDto.getOrgs().get("idt_org__org_code"));
                } else {
                    //过滤出自动同步的组织映射数据
                    deptUserList = deptUserList
                            .stream()
                            .filter(item->"1".equals(item.getDataSource()))
                            .collect(Collectors.toList());
                    // 有用户数据但缺少自动同步的组织用户映射数据  插入映射数据
                    if (deptUserList.isEmpty()) {
                        DeptUser deptUser = new DeptUser();
                        deptUser.setDeptId(oldDeptList.get(0).getId());
                        deptUser.setUserId(oldUser.getId());
                        //“1”为IAM同步，“2”为手动赋予
                        deptUser.setDataSource("1");
                        insertDeptUserList.add(deptUser);
                    } else {
                        // 既有用户数据又有自动同步的组织用户映射数据    更新映射数据
                        DeptUser oldDeptUser = deptUserList.get(0);
                        oldDeptUser.setDeptId(oldDeptList.get(0).getId());
                        deptUserDao.update(oldDeptUser);
                    }
                }
            } else {
                // 插入用户
                this.insertFiller(operator, date, user);
                insertUserList.add(user);
                // 查询对应组织的id 用于插入组织用户关系映射表
                map1.put("deptCode", syncDto.getOrgs().get("idt_org__org_code"));
                List<Dept> deptCopy1VOS = deptDao.selectByParams(map1);
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(deptCopy1VOS)) {
                    log.error("bpim组织表中没有找到组织编码deptCode为{}的数据", syncDto.getOrgs().get("idt_org__org_code"));
                } else {
                    DeptUser deptUser = new DeptUser();
                    deptUser.setDeptId(deptCopy1VOS.get(0).getId());
                    deptUser.setUserId(user.getId());
                    //“1”为IAM同步，“2”为手动赋予
                    deptUser.setDataSource("1");
                    insertDeptUserList.add(deptUser);
                }
                // 设置默认角色
                UserRole userRole = new UserRole();
                userRole.setUserId(user.getId());
                userRole.setRoleId(role.getId());
                userRoleList.add(userRole);
                // 设置租户用户表
                TenantUser tenantUser = new TenantUser();
                tenantUser.setId(UUIDUtil.getUUID());
                tenantUser.setUserId(user.getId());
                tenantUser.setTenantId(role.getTenantId());
                tenantUser.setUserType(NormalEnum.NORMAL.getCode());
                tenantUserList.add(tenantUser);
            }
        }
        try {
            // 批量插入用户
            if (!insertUserList.isEmpty()) {
                userDao.insertBatchWithPrimaryKey(insertUserList);
            }
            // 批量插入组织用户映射
            if (!insertDeptUserList.isEmpty()) {
                this.insertFillers(insertDeptUserList);
                deptUserDao.insertBatchWithPrimaryKey(insertDeptUserList);
            }
            // 批量插入用户角色表
            if (!userRoleList.isEmpty()) {
                userRoleDao.insertBatch(userRoleList);
            }
            // 批量插入租户用户表
            if (!tenantUserList.isEmpty()) {
                tenantUserService.doCreateBatchById(tenantUserList);
            }
            return BaseResponse.success();
        } catch (Exception e) {
            log.error("插入数据到数据库中报错，{}", e.getMessage());
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 实体赋值
     *
     * @param syncDto
     * @return
     */
    private User changeUser(UserDTO syncDto) {
        User user = new User();
        user.setUserName(syncDto.getUserName());
        user.setStaffCode(syncDto.getStaffCode());
        user.setCnName(syncDto.getCnName());
        user.setPhone(syncDto.getPhone());
        user.setEmail(syncDto.getEmail());
        user.setUserStatus(syncDto.getUserStatus());
        if (!StringUtils.isEmpty(syncDto.getEnName())) {
            user.setEnName(syncDto.getEnName());
        } else {
            if (StringUtils.isNotEmpty(syncDto.getCnName())) {
                // 如果cnName是中文就转化为英文
            }
        }
        return user;
    }

    /**
     * 给用户表插入默认字段
     *
     * @param operator
     * @param operationTime
     * @param entity
     */
    private void insertFiller(String operator, Date operationTime, User entity) {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setId(UUIDUtil.getUUID());
        }
        if ("1".equals(entity.getUserStatus()) || "3".equals(entity.getUserStatus())) {
            entity.setEnabled(YesOrNoEnum.YES.getCode());
        } else {
            entity.setEnabled(YesOrNoEnum.NO.getCode());
        }
        entity.setUserType(NormalEnum.NORMAL.getCode());
        entity.setCreator(operator);
        entity.setCreateTime(operationTime);
        entity.setModifier(operator);
        entity.setModifyTime(operationTime);
    }

    /**
     * 组织用户表插入id
     *
     * @param insertDeptUser
     */
    private void insertFillers(List<DeptUser> insertDeptUser) {
        insertDeptUser.stream().forEach(x -> {
            if (StringUtils.isBlank(x.getId())) {
                x.setId(UUIDUtil.getUUID());
            }
        });
    }

    @Override
    public void syncAllUser() {
        log.info("开始全量同步IAM用户数据");
        try {
            newDcpFeign.callExternalApi(TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.IAM.getCode(),
                    ApiCategoryEnum.REAL_ALL_USER.getCode(), MapUtil.newHashMap());
        } catch (Exception e) {
            log.error("数据执行报错，{}，{}", e.getMessage(), e.getCause().toString());
        }
    }

    /**
     * 暂时只同步用户组织映射关系
     *
     * @param list 同步到的用户
     * @return
     */
    @Override
    public BaseResponse<String> syncAllUserData(List<UserDTO> list) {
        try {
            // 查询用户表
            HashMap<String, Object> map = new HashMap<>(1);
            // 组织用户关系映射新增表
            List<DeptUser> insertDeptUserList = new ArrayList<>();
            // 组织用户关系映射更新表
            List<DeptUser> updateDeptUserList = new ArrayList<>();
            // 清除同一个用户进行多次操作
            Map<String, UserDTO> collect = list.stream().collect(Collectors.toMap(
                    UserDTO::getUserName,
                    item -> item,
                    (item1, item2) -> item1.getRequestLogCreateTime().compareTo(item2.getRequestLogCreateTime()) > 0
                            ? item1 : item2
            ));
            Collection<UserDTO> values = collect.values();
            Set<String> userNames = collect.keySet();
            // 根据同步的数据获取当前用户集合 并按照工号分类
            map.put("userNames", userNames);
            List<User> oldDataList = userDao.selectByParams(map);
            Map<String, List<User>> userMap = oldDataList.stream().collect(Collectors.groupingBy(User::getUserName));
            //获取用户id集合
            List<String> userIds = oldDataList.stream().map(User::getId).collect(Collectors.toList());
            // 数据遍历 并按照用户id分类
            List<DeptUser> deptUserList = deptUserDao.selectByParams(ImmutableMap.of("userIds", userIds, "dataSource", "1"));
            Map<String, List<DeptUser>> deptUserMap = deptUserList.stream().collect(Collectors.groupingBy(DeptUser::getUserId));
            for (UserDTO syncUser : values) {
                //判断该用户(工号)是否在BPIM的用户表中
                String userName = syncUser.getUserName();
                if (userMap.containsKey(userName)){
                    User oldUser = userMap.get(userName).get(0);
                    //判断该用户(id)是否在用户组织映射表中
                    String id = oldUser.getId();
                    if (deptUserMap.containsKey(id)){
                        DeptUser deptUser = deptUserMap.get(id).get(0);
                        deptUser.setDeptId(syncUser.getOrgs().get("idt_org__id"));
                        updateDeptUserList.add(deptUser);
                    } else {
                        DeptUser deptUser = new DeptUser();
                        deptUser.setUserId(id);
                        deptUser.setDeptId(syncUser.getOrgs().get("idt_org__id"));
                        deptUser.setDataSource("1");
                        insertDeptUserList.add(deptUser);
                    }
                }
            }
            // 批量插入组织用户映射
            if (!insertDeptUserList.isEmpty()) {
                this.insertFillers(insertDeptUserList);
                deptUserDao.insertBatchWithPrimaryKey(insertDeptUserList);
            }
            if (!updateDeptUserList.isEmpty()){
                deptUserDao.updateBatch(updateDeptUserList);
            }
            return BaseResponse.success();
        } catch (Exception e) {
            log.error("插入数据到数据库中报错，{},{}", e.getMessage(),e.getCause().toString());
            throw new BusinessException(e.getMessage());
        }
    }


	@Override
	public List<LabelValue<String>> selectOrderPlannerDropDown() {
		List<User> users = userDao.selectOrderPlannerDropDown();
		List<LabelValue<String>> data = users.stream().map(item -> new LabelValue<>(item.getCnName(), item.getId()))
                .collect(Collectors.toList());
		return data;
	}

}