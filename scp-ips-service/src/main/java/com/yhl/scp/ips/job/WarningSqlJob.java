package com.yhl.scp.ips.job;

import com.cronutils.model.Cron;
import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.model.time.ExecutionTime;
import com.cronutils.parser.CronParser;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.ips.warning.dto.WarningSqlSettingDTO;
import com.yhl.scp.ips.warning.service.WarningSqlSettingService;
import com.yhl.scp.ips.warning.vo.WarningSqlSettingVO;
import io.seata.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * description:预警配置
 * author：李杰
 * email: <EMAIL>
 * date: 2025/3/10
 */
@Configuration
@Slf4j
public class WarningSqlJob {

    @Resource
    private WarningSqlSettingService warningSqlSettingService;

    @XxlJob("warningSqlJob")
    public void syncWarningSql() {
        XxlJobHelper.log("开始执行预警配置");
        // 获取当前时间
        ZonedDateTime now = ZonedDateTime.now();
        Date date = Date.from(now.toInstant());
        // 获取当前时间30分钟后的时刻
        ZonedDateTime tenMinutesLater = now.plus(30, ChronoUnit.MINUTES);
        //获取数据库的预警配置数据
        List<WarningSqlSettingVO> warningSqlSettingVOS = warningSqlSettingService.selectAll();
        warningSqlSettingVOS = warningSqlSettingVOS.stream()
                .filter(x -> YesOrNoEnum.YES.getCode().equals(x.getEnabled())
//                        && StringUtils.isNotBlank(x.getSqlContext())
                        && StringUtils.isNotBlank(x.getPollingTime()))
                .collect(Collectors.toList());
        if (warningSqlSettingVOS.isEmpty()) {
            XxlJobHelper.log("经过状态生效，sql语句不为空和轮询时间不为空的筛选已经没有数据了。");
        } else {
            //获取30分钟内的数据
            ArrayList<String> arrayList = new ArrayList<>();
            //获取30分钟内需要调用的数据
            List<WarningSqlSettingDTO> syncData = new ArrayList<>();
            CronParser cronParser = new CronParser(CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ));
            for (WarningSqlSettingVO warningVO : warningSqlSettingVOS) {
                Cron parse = cronParser.parse(warningVO.getPollingTime());
                ExecutionTime executionTime = ExecutionTime.forCron(parse);
                ZonedDateTime nextExecution = executionTime.nextExecution(now).orElseThrow(() -> new RuntimeException(warningVO.getWarningDescription() + "无法计算该预警的下次执行时间"));
                if (!nextExecution.isBefore(now) && !nextExecution.isAfter(tenMinutesLater)) {
                    WarningSqlSettingDTO warningSqlSettingDTO = new WarningSqlSettingDTO();
                    BeanUtils.copyProperties(warningVO, warningSqlSettingDTO);
                    syncData.add(warningSqlSettingDTO);
                }
            }
            log.info("接下来30分钟内会触发{}个预警配置",syncData.size());
            if (!syncData.isEmpty()){
                syncData.stream().forEach(x -> {
                    BaseResponse send = warningSqlSettingService.send(x, date);
                    if (!send.getSuccess()) {
                        arrayList.add(x.getWarningDescription());
                    }
                });
                XxlJobHelper.log(arrayList + "这些预警失效或者有报错，没有数据进行发送。");
            }
        }
        XxlJobHelper.log("结束执行预警配置");
    }
}
