package com.yhl.scp.ips.rbac.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.i18n.service.UserDefaultLanguageService;
import com.yhl.scp.common.enums.UserTypeEnum;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.object.entity.ObjectI18n;
import com.yhl.scp.ips.object.service.ObjectI18nService;
import com.yhl.scp.ips.rbac.dao.ResourceDao;
import com.yhl.scp.ips.rbac.dao.RoleResourceDao;
import com.yhl.scp.ips.rbac.entity.Resource;
import com.yhl.scp.ips.rbac.entity.Role;
import com.yhl.scp.ips.rbac.service.ResourceService;
import com.yhl.scp.ips.rbac.service.RoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <code>ResourceServiceImpl</code>
 * <p>
 * ResourceServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-07-17 20:21:06
 */
@Slf4j
@Service
public class ResourceServiceImpl implements ResourceService {

    @Autowired
    private ResourceDao resourceDao;
    @Autowired
    private RoleResourceDao roleResourceDao;
    @Autowired
    private ObjectI18nService objectI18nService;
    @Autowired
    private RoleService roleService;

    @Autowired
    private UserDefaultLanguageService defaultLanguageService;


    @Override
    public BaseResponse doCreate(Resource resource) {

        if (StringUtils.isEmpty(resource.getResourceType())) {
            return BaseResponse.error("资源类型不能为空:菜单/控件");
        }
        // 租户创建菜单
        if (StringUtils.isNotEmpty(SystemHolder.getTenantId())) {
            resource.setTenantId(SystemHolder.getTenantId());
        }
        if (StringUtils.isEmpty(resource.getId())) {
            resource.setId(UUIDUtil.getUUID());
        }
        Resource parentResource = resourceDao.selectByPrimaryKey(resource.getParentId());
        if (StringUtils.isEmpty(parentResource.getParentId())) {
            return BaseResponse.error("用户不能创建二级菜单");
        }
        resource.setModuleCode(parentResource.getModuleCode());
        resource.setEnabled(YesOrNoEnum.YES.getCode());
        resourceDao.insert(resource);
        return BaseResponse.success();
    }

    @Override
    public BaseResponse doUpdate(Resource resource) {

        if (StringUtils.isEmpty(resource.getResourceType())) {
            return BaseResponse.error("资源类型不能为空:菜单/控件");
        }

        Resource checkResource = resourceDao.selectByPrimaryKey(resource.getId());
        if (!UserTypeEnum.SYSTEM_ADMIN.getCode().equals(SystemHolder.getUserType()) && StringUtils.isEmpty(checkResource.getTenantId())) {
            return BaseResponse.error("非租户私有菜单不允许租户修改");
        }
        Resource parentResource = resourceDao.selectByPrimaryKey(resource.getParentId());
        if (!StringUtils.isEmpty(parentResource.getModuleCode())) {
            resource.setModuleCode(parentResource.getModuleCode());
        } else {
            resource.setModuleCode(checkResource.getModuleCode());
        }
        resource.setEnabled(YesOrNoEnum.YES.getCode());
        resourceDao.updateByPrimaryKey(resource);
        return BaseResponse.success("菜单修改成功");
    }

    @Override
    public BaseResponse doDelete(String resourceId) {
        Resource checkResource = resourceDao.selectByPrimaryKey(resourceId);
        if (!UserTypeEnum.SYSTEM_ADMIN.getCode().equals(SystemHolder.getUserType()) && StringUtils.isEmpty(checkResource.getTenantId())) {
            return BaseResponse.error("非租户私有菜单不允许租户删除");
        }
        roleResourceDao.deleteByResourceId(resourceId);
        Resource resource = resourceDao.selectByPrimaryKey(resourceId);
        resource.setEnabled(YesOrNoEnum.NO.getCode());
        resourceDao.updateByPrimaryKey(resource);

        return BaseResponse.success();
    }

    @Override
    public Resource selectById(String resourceId) {
        return resourceDao.selectByPrimaryKey(resourceId);
    }

    @Override
    public List<Resource> selectPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return resourceDao.selectByCondition(sortParam, queryCriteriaParam, SystemHolder.getTenantId());
    }

    @Override
    public List<Resource> selectByUserId(String userId) {

        if (UserTypeEnum.SYSTEM_ADMIN.getCode().equals(SystemHolder.getUserType())) {
            return resourceDao.selectForSystemAdmin();
        }

        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("tenantId", SystemHolder.getTenantId());
        // 有权限的菜单资源
        List<Resource> resources = resourceDao.selectByParams(params);
        List<Role> roles = roleService.selectByUserId(userId);
        log.info("用户角色有： {}, 大小有 {}", roles.stream().map(Role::toString).collect(Collectors.joining(", ")), roles.size());
        List<Resource> roleWidgets = selectWidgetResourcesByRoleIds(roles.stream().map(Role::getId).collect(Collectors.toList()));
        // 角色对应的按钮组件id
        List<String> resourceIds = roleWidgets.stream().map(Resource::getId).collect(Collectors.toList());
        log.info("角色对应的按钮组件id 是： {}, 大小有 {}", resourceIds, resourceIds.size());
        // 有权限菜单下所有按钮组件
        List<Resource> resourceWidgets = selectWidgetResourcesByParentIds(resources.stream().map(Resource::getId).collect(Collectors.toList()));
        log.info("用户角色有： {}, 大小有 {}", resourceWidgets.stream().map(Resource::toString).collect(Collectors.joining(", ")), resourceWidgets.size());
        for (Resource resource : resourceWidgets) {
            if (resourceIds.contains(resource.getId())) {
                resource.setWidgetAuthority(YesOrNoEnum.YES.getCode());
            } else {
                resource.setWidgetAuthority(YesOrNoEnum.NO.getCode());
            }
            resources.add(resource);
        }

//        String language = defaultLanguageService.getDefaultLanguageByUserId(SystemHolder.getUserId());
//        log.info("1 selectByUserId language is " + language);
//	    if(StringUtils.isEmpty(language)) {
//	    	language = LocaleContextHolder.getLocale().toString();
//	    	log.info("2 selectByUserId language is " + language);
//	    }
        List<ObjectI18n> objectI18ns = objectI18nService.selectByObjectType("RESOURCE");
        Map<String, ObjectI18n> i18nMap = objectI18ns.stream().collect(Collectors.toMap(item -> item.getObjectId() + item.getLanguageCode(), v -> v));
        for (Resource resource : resources) {
            ObjectI18n objectI18n = i18nMap.get(resource.getId() + SystemHolder.getLanguage());
            if (null != objectI18n && StringUtils.isNotEmpty(objectI18n.getDesc())) {
                resource.setResourceName(objectI18n.getDesc());
            }
        }
        return resources;
    }

    private List<Resource> selectWidgetResourcesByRoleIds(List<String> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return new ArrayList<>();
        }
        return resourceDao.selectWidgetResourcesByRoleIds(roleIds);
    }

    private List<Resource> selectWidgetResourcesByParentIds(List<String> parentIds) {
        if (CollectionUtils.isEmpty(parentIds)) {
            return new ArrayList<>();
        }
        return resourceDao.selectWidgetResourcesByParentIds(parentIds);
    }

    @Override
    public List<Resource> selectTreeById(String id) {
        return resourceDao.selectTreeById(id, SystemHolder.getTenantId());
    }

    @Override
    public List<Resource> selectAll() {
        return resourceDao.selectAll();
    }

    @Override
    public List<Resource> selectByIds(List<String> resourceIds) {
        if (CollectionUtils.isEmpty(resourceIds)) {
            return new ArrayList<>();
        }
        return resourceDao.selectByIds(resourceIds);
    }

}
