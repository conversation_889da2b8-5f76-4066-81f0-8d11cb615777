package com.yhl.scp.mrp.inventory.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.inventory.dto.InventoryAlternativeRelationshipDTO;
import com.yhl.scp.mrp.inventory.dto.InventoryQuayDetailDTO;
import com.yhl.scp.mrp.inventory.service.InventoryQuayDetailService;
import com.yhl.scp.mrp.inventory.vo.InventoryAlternativeRelationshipVO;
import com.yhl.scp.mrp.inventory.vo.InventoryOurFactoryDetailVO;
import com.yhl.scp.mrp.inventory.vo.InventoryQuayDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>InventoryQuayDetailController</code>
 * <p>
 * 原片码头库存批次明细控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 20:25:15
 */

@Slf4j
@Api(tags = "原片码头库存批次明细控制器")
@RestController
@RequestMapping("inventoryQuayDetail")
public class InventoryQuayDetailController extends BaseController {

    @Resource
    private InventoryQuayDetailService inventoryQuayDetailService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<InventoryQuayDetailVO>> page(
            @RequestParam(value = "overdueSort", required = false, defaultValue = "NO") String overdueSort
    ) {
        List<InventoryQuayDetailVO> inventoryQuayDetailList =
                inventoryQuayDetailService.selectByPage(getPagination(), getSortParam(),
                        getQueryCriteriaParam(), overdueSort);
        PageInfo<InventoryQuayDetailVO> pageInfo = new PageInfo<>(inventoryQuayDetailList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody InventoryQuayDetailDTO inventoryQuayDetailDTO) {
        return inventoryQuayDetailService.doCreate(inventoryQuayDetailDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody InventoryQuayDetailDTO inventoryQuayDetailDTO) {
        return inventoryQuayDetailService.doUpdate(inventoryQuayDetailDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        inventoryQuayDetailService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<InventoryQuayDetailVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, inventoryQuayDetailService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "可用查找")
    @GetMapping(value = "availableSearch")
    public BaseResponse<List<InventoryAlternativeRelationshipVO>> availableSearch(@RequestParam("productCode") String productCode,
                                                                                  @RequestParam("alternativeType") String alternativeType,
                                                                                  @RequestParam(value = "cuttingRatePercentage", required = false, defaultValue = "0.8") String cuttingRatePercentage) {
        List<InventoryAlternativeRelationshipVO> availableSearch = inventoryQuayDetailService.availableSearch(productCode, alternativeType, cuttingRatePercentage);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, availableSearch);
    }

    @ApiOperation(value = "原片替代（码头）")
    @PostMapping(value = "alternative")
    public BaseResponse<Void> alternative(@RequestBody List<InventoryAlternativeRelationshipDTO> list) {
        return inventoryQuayDetailService.alternative(list);
    }
}
