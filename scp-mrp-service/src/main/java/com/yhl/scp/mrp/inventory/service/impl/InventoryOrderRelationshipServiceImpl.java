package com.yhl.scp.mrp.inventory.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.stock.service.InventoryBatchDetailService;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.enums.ProductTypeEnum;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mrp.inventory.convertor.InventoryAlternativeRelationshipConvertor;
import com.yhl.scp.mrp.inventory.domain.entity.InventoryAlternativeRelationshipDO;
import com.yhl.scp.mrp.inventory.domain.service.InventoryAlternativeRelationshipDomainService;
import com.yhl.scp.mrp.inventory.dto.InventoryAlternativeRelationshipDTO;
import com.yhl.scp.mrp.inventory.infrastructure.dao.InventoryAlternativeRelationshipDao;
import com.yhl.scp.mrp.inventory.infrastructure.po.InventoryAlternativeRelationshipPO;
import com.yhl.scp.mrp.inventory.service.InventoryAlternativeRelationshipService;
import com.yhl.scp.mrp.inventory.service.InventoryOrderRelationshipService;
import com.yhl.scp.mrp.inventory.service.InventoryQuayDetailService;
import com.yhl.scp.mrp.inventory.vo.InventoryAlternativeRelationshipVO;
import com.yhl.scp.mrp.inventory.vo.InventoryOrderRelationshipVO;
import com.yhl.scp.mrp.inventory.vo.InventoryQuayDetailVO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanInventoryShiftService;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanVersionService;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanInventoryShiftVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>InventoryOrderRelationshipServiceImpl</code>
 * <p>
 * 库存与订单关系应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-25 10:25:16
 */
@Slf4j
@Service
public class InventoryOrderRelationshipServiceImpl  implements InventoryOrderRelationshipService {

    @Resource
    private MaterialArrivalTrackingService materialArrivalTrackingService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private MpsFeign mpsFeign;

    @Override
    public List<InventoryOrderRelationshipVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {

        // 获取产品需求库存
        // 收集本厂编码
        // 根据本厂编码获取BOM明细
        // 收集物料编码
        // 获取实时库存
        // 获取材料与供应商关系
        // 获取到货跟踪


        return Collections.emptyList();
    }
}
