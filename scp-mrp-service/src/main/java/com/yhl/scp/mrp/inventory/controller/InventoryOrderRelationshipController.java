package com.yhl.scp.mrp.inventory.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.inventory.service.InventoryAlternativeRelationshipService;
import com.yhl.scp.mrp.inventory.service.InventoryOrderRelationshipService;
import com.yhl.scp.mrp.inventory.vo.InventoryOceanFreightPlacesVO;
import com.yhl.scp.mrp.inventory.vo.InventoryOrderRelationshipVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

/**
 * <code>InventoryOrderRelationshipController</code>
 * <p>
 * 库存与订单关系控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-25 10:25:15
 */
@Slf4j
@Api(tags = "库存与订单关系控制器")
@RestController
@RequestMapping("inventoryOrderRelationship")
public class InventoryOrderRelationshipController extends BaseController {

    @Resource
    private InventoryOrderRelationshipService inventoryOrderRelationshipService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<InventoryOrderRelationshipVO>> page() {
        List<InventoryOrderRelationshipVO> inventoryOceanFreightPlacesList = inventoryOrderRelationshipService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<InventoryOrderRelationshipVO> pageInfo = new PageInfo<>(inventoryOceanFreightPlacesList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

}
