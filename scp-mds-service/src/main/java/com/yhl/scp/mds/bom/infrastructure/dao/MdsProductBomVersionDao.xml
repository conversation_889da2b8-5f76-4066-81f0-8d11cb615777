<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.bom.infrastructure.dao.MdsProductBomVersionDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.bom.infrastructure.po.ProductBomVersionPO">
        <!--@Table mds_rou_product_bom_version-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="version_name" jdbcType="VARCHAR" property="versionName"/>
        <result column="bom_version" jdbcType="VARCHAR" property="bomVersion"/>
        <result column="product_series_id" jdbcType="VARCHAR" property="productSeriesId"/>
        <result column="resource_id" jdbcType="VARCHAR" property="resourceId"/>
        <result column="stock_point_id" jdbcType="VARCHAR" property="stockPointId"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="effective_time" jdbcType="TIMESTAMP" property="effectiveTime"/>
        <result column="expiry_time" jdbcType="TIMESTAMP" property="expiryTime"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="bill_sequence_id" jdbcType="VARCHAR" property="billSequenceId"/>
        <result column="version_value" jdbcType="VARCHAR" property="versionValue"/>
        <result column="product_sync_code" jdbcType="VARCHAR" property="productSyncCode"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mds.bom.vo.ProductBomVersionVO">
    	<result column="product_code" jdbcType="VARCHAR" property="productCode"/>
    	<result column="product_name" jdbcType="VARCHAR" property="productName"/>
    	<result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
    	<result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
        <result column="product_type" jdbcType="VARCHAR" property="productType"/>
        <result column="product_length" jdbcType="VARCHAR" property="productLength"/>
        <result column="product_width" jdbcType="VARCHAR" property="productWidth"/>
        <result column="product_area" jdbcType="VARCHAR" property="productArea"/>

    </resultMap>
    <sql id="Base_Column_List">
		id,
		version_name,
		bom_version,
		product_series_id,
		resource_id,
		stock_point_id,
		product_id,
		effective_time,
		expiry_time,
		STATUS,
		remark,
		enabled,
		creator,
		create_time,
		modifier,
		modify_time,
		bill_sequence_id,
		version_value,product_sync_code
    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List" />,
        product_code,
        product_name,
        stock_point_code,
        stock_point_name,
        product_type,
        product_length,
        product_width,
        product_area
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.ids != null and params.ids.size() > 0">
                and id in
                <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.versionName != null and params.versionName != ''">
                and version_name = #{params.versionName,jdbcType=VARCHAR}
            </if>
            <if test="params.bomVersion != null and params.bomVersion != ''">
                and bom_version = #{params.bomVersion,jdbcType=VARCHAR}
            </if>
            <if test="params.productSeriesId != null and params.productSeriesId != ''">
                and product_series_id = #{params.productSeriesId,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceId != null and params.resourceId != ''">
                and resource_id = #{params.resourceId,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointId != null and params.stockPointId != ''">
                and stock_point_id = #{params.stockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.productId != null and params.productId != ''">
                and product_id = #{params.productId,jdbcType=VARCHAR}
            </if>
            <if test="params.productIsNull != null and params.productIsNull != ''">
                and product_id is null
            </if>
            <if test="params.effectiveTime != null">
                and effective_time = #{params.effectiveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.expiryTime != null">
                and expiry_time = #{params.expiryTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.status != null and params.status != ''">
                and status = #{params.status,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.billSequenceId != null and params.billSequenceId != ''">
                and bill_sequence_id = #{params.billSequenceId,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=VARCHAR}
            </if>
            <if test="params.billSequenceIds != null and params.billSequenceIds != ''">
                and bill_sequence_id in
                <foreach collection="params.billSequenceIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
			<if test="params.productIds != null and params.productIds != ''">
                and product_id in
                <foreach collection="params.productIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productSyncCode != null and params.productSyncCode != ''">
                and product_sync_code = #{params.productSyncCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productSyncCodeIsNotNull != null and params.productSyncCodeIsNotNull != ''">
                and product_sync_code is not null
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productName != null and params.productName != ''">
                and product_name = #{params.productName,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointName != null and params.stockPointName != ''">
                and stock_point_name = #{params.stockPointName,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_rou_product_bom_version
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_rou_product_bom_version
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_mds_rou_product_bom_version
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>

    <select id="selectVO2ByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_mds_rou_product_bom_version
        <include refid="Base_Where_Condition" />
    </select>

    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_rou_product_bom_version
        <include refid="Base_Where_Condition" />
    </select>

    <!-- 条件查询 VO -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List" />
        from v_mds_rou_product_bom_version
        <include refid="Base_Where_Condition" />
    </select>

    <select id="selectByBillSequenceIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_rou_product_bom_version
        where bill_sequence_id in
        <foreach collection="billSequenceIds" item="billSequenceId" index="index" open="(" separator="," close=")">
            #{billSequenceId,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mds.bom.infrastructure.po.ProductBomVersionPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_rou_product_bom_version(
        id,
        version_name,
        bom_version,
        product_series_id,
        resource_id,
        stock_point_id,
        product_id,
        effective_time,
        expiry_time,
        status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        bill_sequence_id,
        version_value,
        product_sync_code)
        values (
        #{id,jdbcType=VARCHAR},
        #{versionName,jdbcType=VARCHAR},
        #{bomVersion,jdbcType=VARCHAR},
        #{productSeriesId,jdbcType=VARCHAR},
        #{resourceId,jdbcType=VARCHAR},
        #{stockPointId,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR},
        #{effectiveTime,jdbcType=TIMESTAMP},
        #{expiryTime,jdbcType=TIMESTAMP},
        #{status,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{billSequenceId,jdbcType=VARCHAR},
        #{versionValue,jdbcType=VARCHAR},
        #{productSyncCode,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mds.bom.infrastructure.po.ProductBomVersionPO">
        insert into mds_rou_product_bom_version(
        id,
        version_name,
        bom_version,
        product_series_id,
        resource_id,
        stock_point_id,
        product_id,
        effective_time,
        expiry_time,
        status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        bill_sequence_id,
        version_value,
        product_sync_code)
        values (
        #{id,jdbcType=VARCHAR},
        #{versionName,jdbcType=VARCHAR},
        #{bomVersion,jdbcType=VARCHAR},
        #{productSeriesId,jdbcType=VARCHAR},
        #{resourceId,jdbcType=VARCHAR},
        #{stockPointId,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR},
        #{effectiveTime,jdbcType=TIMESTAMP},
        #{expiryTime,jdbcType=TIMESTAMP},
        #{status,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{billSequenceId,jdbcType=VARCHAR},
        #{versionValue,jdbcType=VARCHAR},
        #{productSyncCode,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_rou_product_bom_version(
        id,
        version_name,
        bom_version,
        product_series_id,
        resource_id,
        stock_point_id,
        product_id,
        effective_time,
        expiry_time,
        status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        bill_sequence_id,
        version_value,
        product_sync_code)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.versionName,jdbcType=VARCHAR},
        #{entity.bomVersion,jdbcType=VARCHAR},
        #{entity.productSeriesId,jdbcType=VARCHAR},
        #{entity.resourceId,jdbcType=VARCHAR},
        #{entity.stockPointId,jdbcType=VARCHAR},
        #{entity.productId,jdbcType=VARCHAR},
        #{entity.effectiveTime,jdbcType=TIMESTAMP},
        #{entity.expiryTime,jdbcType=TIMESTAMP},
        #{entity.status,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.billSequenceId,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=VARCHAR},
        #{entity.productSyncCode,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_rou_product_bom_version(
        id,
        version_name,
        bom_version,
        product_series_id,
        resource_id,
        stock_point_id,
        product_id,
        effective_time,
        expiry_time,
        status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        bill_sequence_id,
        version_value,
        product_sync_code)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.versionName,jdbcType=VARCHAR},
        #{entity.bomVersion,jdbcType=VARCHAR},
        #{entity.productSeriesId,jdbcType=VARCHAR},
        #{entity.resourceId,jdbcType=VARCHAR},
        #{entity.stockPointId,jdbcType=VARCHAR},
        #{entity.productId,jdbcType=VARCHAR},
        #{entity.effectiveTime,jdbcType=TIMESTAMP},
        #{entity.expiryTime,jdbcType=TIMESTAMP},
        #{entity.status,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.billSequenceId,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=VARCHAR},
        #{entity.productSyncCode,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mds.bom.infrastructure.po.ProductBomVersionPO">
        update mds_rou_product_bom_version set
        version_name = #{versionName,jdbcType=VARCHAR},
        bom_version = #{bomVersion,jdbcType=VARCHAR},
        product_series_id = #{productSeriesId,jdbcType=VARCHAR},
        resource_id = #{resourceId,jdbcType=VARCHAR},
        stock_point_id = #{stockPointId,jdbcType=VARCHAR},
        product_id = #{productId,jdbcType=VARCHAR},
        effective_time = #{effectiveTime,jdbcType=TIMESTAMP},
        expiry_time = #{expiryTime,jdbcType=TIMESTAMP},
        status = #{status,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        bill_sequence_id = #{billSequenceId,jdbcType=VARCHAR},
        product_sync_code = #{productSyncCode,jdbcType=VARCHAR}
        version_value = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mds.bom.infrastructure.po.ProductBomVersionPO">
        update mds_rou_product_bom_version
        <set>
            <if test="item.versionName != null and item.versionName != ''">
                version_name = #{item.versionName,jdbcType=VARCHAR},
            </if>
            <if test="item.bomVersion != null and item.bomVersion != ''">
                bom_version = #{item.bomVersion,jdbcType=VARCHAR},
            </if>
            <if test="item.productSeriesId != null and item.productSeriesId != ''">
                product_series_id = #{item.productSeriesId,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceId != null and item.resourceId != ''">
                resource_id = #{item.resourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.effectiveTime != null">
                effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.expiryTime != null">
                expiry_time = #{item.expiryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.status != null and item.status != ''">
                status = #{item.status,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.billSequenceId != null and item.billSequenceId != ''">
                bill_sequence_id = #{item.billSequenceId,jdbcType=VARCHAR},
            </if>
            <if test="item.productSyncCode != null and item.productSyncCode != ''">
                product_sync_code = #{item.productSyncCode,jdbcType=VARCHAR},
            </if>
            version_value = version_value + 1,
        </set>
        where id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_rou_product_bom_version
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="version_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bom_version = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.bomVersion,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_series_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productSeriesId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resourceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="effective_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effectiveTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="expiry_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expiryTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.status,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="bill_sequence_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.billSequenceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="product_sync_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productSyncCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mds_rou_product_bom_version 
        <set>
            <if test="item.versionName != null and item.versionName != ''">
                version_name = #{item.versionName,jdbcType=VARCHAR},
            </if>
            <if test="item.bomVersion != null and item.bomVersion != ''">
                bom_version = #{item.bomVersion,jdbcType=VARCHAR},
            </if>
            <if test="item.productSeriesId != null and item.productSeriesId != ''">
                product_series_id = #{item.productSeriesId,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceId != null and item.resourceId != ''">
                resource_id = #{item.resourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.effectiveTime != null">
                effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.expiryTime != null">
                expiry_time = #{item.expiryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.status != null and item.status != ''">
                status = #{item.status,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.billSequenceId != null and item.billSequenceId != ''">
                bill_sequence_id = #{item.billSequenceId,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.productSyncCode != null and item.productSyncCode != ''">
                product_sync_code = #{item.productSyncCode,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <update id="doLogicDeleteBatch">
        <foreach collection="deleteProductBomVersionList" index="index" item="item" separator=";">
            update mds_rou_product_bom_version
            set
            enabled = 'NO',
            version_value = version_value + 1
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mds_rou_product_bom_version where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_rou_product_bom_version where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <select id="selectIdByProductCode" resultType="java.lang.String">
        select id
        from v_mds_rou_product_bom_version
        where product_code = #{productCode}
    </select>

    <select id="selectVOByPrimaryKeys" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List" />
        from v_mds_rou_product_bom_version
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>
