package com.yhl.scp.dfp.loading.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.loading.dto.DfpDemandForecastAttachmentsDTO;
import com.yhl.scp.dfp.loading.service.DfpDemandForecastAttachmentsService;
import com.yhl.scp.dfp.loading.vo.DfpDemandForecastAttachmentsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <code>DfpDemandForecastAttachmentsController</code>
 * <p>
 * 源文件管理控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-03 14:38:29
 */
@Slf4j
@Api(tags = "源文件管理控制器")
@RestController
@RequestMapping("dfpDemandForecastAttachments")
public class DfpDemandForecastAttachmentsController extends BaseController {

    @Resource
    private DfpDemandForecastAttachmentsService dfpDemandForecastAttachmentsService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<DfpDemandForecastAttachmentsVO>> page() {
        List<DfpDemandForecastAttachmentsVO> dfpDemandForecastAttachmentsList =
                dfpDemandForecastAttachmentsService.selectByPage(getPagination(), getSortParam(),
                        getQueryCriteriaParam());
        PageInfo<DfpDemandForecastAttachmentsVO> pageInfo = new PageInfo<>(dfpDemandForecastAttachmentsList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody DfpDemandForecastAttachmentsDTO dfpDemandForecastAttachmentsDTO) {
        return dfpDemandForecastAttachmentsService.doCreate(dfpDemandForecastAttachmentsDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody DfpDemandForecastAttachmentsDTO dfpDemandForecastAttachmentsDTO) {
        return dfpDemandForecastAttachmentsService.doUpdate(dfpDemandForecastAttachmentsDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        dfpDemandForecastAttachmentsService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<DfpDemandForecastAttachmentsVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, dfpDemandForecastAttachmentsService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "上传文件")
    @PostMapping(value = "upload")
    public BaseResponse<Void> upload(@RequestParam(value = "versionCode") String versionCode,
    		 						@RequestParam(value = "uploadStatus", required = false) String uploadStatus,
    		 						@RequestBody Map<String, List<MultipartFile>> fileMap) {
        dfpDemandForecastAttachmentsService.upload(versionCode, uploadStatus,
                fileMap);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "上传单个文件测试")
    @PostMapping(value = "uploadFile")
    public BaseResponse<Void> upload(@RequestParam(value = "versionCode") String versionCode,
                                     @RequestParam(value = "oemCode", required = false) String oemCode,
                                     @RequestParam(value = "uploadStatus", required = false) String uploadStatus,
                                     @RequestPart MultipartFile file) {
        dfpDemandForecastAttachmentsService.uploadFile(versionCode,
                oemCode, uploadStatus,file);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "下载文件")
    @GetMapping(value = "download")
    public BaseResponse<Void> download(HttpServletResponse httpServletResponse, @RequestParam(value = "id") String id) {
        dfpDemandForecastAttachmentsService.download(id, httpServletResponse);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "删除文件")
    @PostMapping(value = "remove")
    public BaseResponse<Void> remove(@RequestParam(value = "id") String id) {
        dfpDemandForecastAttachmentsService.remove(id);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "获取服务器文件列表")
    @GetMapping(value = "listObjects")
    public BaseResponse<List<String>> listObjects() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, dfpDemandForecastAttachmentsService.listObjects());
    }

}