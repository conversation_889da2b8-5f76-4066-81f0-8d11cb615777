package com.yhl.scp.dfp.delivery.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.delivery.convertor.DeliveryPlanPublishedCompareDayConvertor;
import com.yhl.scp.dfp.delivery.domain.entity.DeliveryPlanPublishedCompareDayDO;
import com.yhl.scp.dfp.delivery.domain.service.DeliveryPlanPublishedCompareDayDomainService;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanPublishedCompareDayDTO;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanPublishedDTO;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanPublishedCompareDayDao;
import com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPublishedCompareDayPO;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanPublishedCompareDayService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanPublishedService;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedCompareDayVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedCompareDayVO2;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mps.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>DeliveryPlanPublishedCompareDayServiceImpl</code>
 * <p>
 * 发货计划按天对比表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13 14:14:16
 */
@Slf4j
@Service
public class DeliveryPlanPublishedCompareDayServiceImpl extends AbstractService implements DeliveryPlanPublishedCompareDayService {

    @Resource
    private DeliveryPlanPublishedCompareDayDao deliveryPlanPublishedCompareDayDao;

    @Resource
    private DeliveryPlanPublishedCompareDayDomainService deliveryPlanPublishedCompareDayDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private DeliveryPlanPublishedService deliveryPlanPublishedService;

    @Resource
    private IpsFeign ipsFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(DeliveryPlanPublishedCompareDayDTO deliveryPlanPublishedCompareDayDTO) {
        // 0.数据转换
        DeliveryPlanPublishedCompareDayDO deliveryPlanPublishedCompareDayDO = DeliveryPlanPublishedCompareDayConvertor.INSTANCE.dto2Do(deliveryPlanPublishedCompareDayDTO);
        DeliveryPlanPublishedCompareDayPO deliveryPlanPublishedCompareDayPO = DeliveryPlanPublishedCompareDayConvertor.INSTANCE.dto2Po(deliveryPlanPublishedCompareDayDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        deliveryPlanPublishedCompareDayDomainService.validation(deliveryPlanPublishedCompareDayDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(deliveryPlanPublishedCompareDayPO);
        deliveryPlanPublishedCompareDayDao.insert(deliveryPlanPublishedCompareDayPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(DeliveryPlanPublishedCompareDayDTO deliveryPlanPublishedCompareDayDTO) {
        // 0.数据转换
        DeliveryPlanPublishedCompareDayDO deliveryPlanPublishedCompareDayDO = DeliveryPlanPublishedCompareDayConvertor.INSTANCE.dto2Do(deliveryPlanPublishedCompareDayDTO);
        DeliveryPlanPublishedCompareDayPO deliveryPlanPublishedCompareDayPO = DeliveryPlanPublishedCompareDayConvertor.INSTANCE.dto2Po(deliveryPlanPublishedCompareDayDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        deliveryPlanPublishedCompareDayDomainService.validation(deliveryPlanPublishedCompareDayDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(deliveryPlanPublishedCompareDayPO);
        deliveryPlanPublishedCompareDayDao.update(deliveryPlanPublishedCompareDayPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<DeliveryPlanPublishedCompareDayDTO> list) {
        List<DeliveryPlanPublishedCompareDayPO> newList = DeliveryPlanPublishedCompareDayConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        deliveryPlanPublishedCompareDayDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<DeliveryPlanPublishedCompareDayDTO> list) {
        List<DeliveryPlanPublishedCompareDayPO> newList = DeliveryPlanPublishedCompareDayConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        deliveryPlanPublishedCompareDayDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return deliveryPlanPublishedCompareDayDao.deleteBatch(idList);
        }
        return deliveryPlanPublishedCompareDayDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public DeliveryPlanPublishedCompareDayVO selectByPrimaryKey(String id) {
        DeliveryPlanPublishedCompareDayPO po = deliveryPlanPublishedCompareDayDao.selectByPrimaryKey(id);
        return DeliveryPlanPublishedCompareDayConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "DELIVERY_PLAN_PUBLISHED_COMPARE_DAY")
    public List<DeliveryPlanPublishedCompareDayVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "DELIVERY_PLAN_PUBLISHED_COMPARE_DAY")
    public List<DeliveryPlanPublishedCompareDayVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<DeliveryPlanPublishedCompareDayVO> dataList = deliveryPlanPublishedCompareDayDao.selectByCondition(sortParam, queryCriteriaParam);
        DeliveryPlanPublishedCompareDayServiceImpl target = springBeanUtils.getBean(DeliveryPlanPublishedCompareDayServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<DeliveryPlanPublishedCompareDayVO> selectByParams(Map<String, Object> params) {
        List<DeliveryPlanPublishedCompareDayPO> list = deliveryPlanPublishedCompareDayDao.selectByParams(params);
        return DeliveryPlanPublishedCompareDayConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<DeliveryPlanPublishedCompareDayVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.DELIVERY_PLAN_PUBLISHED_COMPARE_DAY.getCode();
    }

    @Override
    public List<DeliveryPlanPublishedCompareDayVO> invocation(List<DeliveryPlanPublishedCompareDayVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }


    @Override
    public void doCompare(List<DeliveryPlanPublishedDTO> oldDataList, String userId, Date startDate) {
        if (CollectionUtils.isEmpty(oldDataList) || StringUtils.isEmpty(userId)) {
            return;
        }
        String startDateStr = DateUtils.dateToString(startDate);
        // 去掉时分秒
        Date startDateTruncateTime = DateUtils.stringToDate(startDateStr);
        // 查询最新的发货计划数据
        List<String> oemCodes = oldDataList.stream().map(DeliveryPlanPublishedDTO::getOemCode)
                .distinct().collect(Collectors.toList());
        List<String> productCodes = oldDataList.stream().map(DeliveryPlanPublishedDTO::getProductCode)
                .distinct().collect(Collectors.toList());
        List<DeliveryPlanPublishedVO> newDeliveryPlanPublishedVOS = deliveryPlanPublishedService.selectByParams(
                ImmutableMap.of("oemCodes", oemCodes, "productCodes", productCodes, "demandTimeStart", startDateStr));
        List<CollectionValueVO> collectionValueList = ipsFeign.getByCollectionCode("DELIVERY_PLAN_PUBLISHED_COMPARE_DAY");
        if (com.yhl.platform.common.utils.CollectionUtils.isEmpty(collectionValueList)) {
            log.info("未维护字典表DELIVERY_PLAN_PUBLISHED_COMPARE_DAY");
            return;
        }
        List<String> dayList = collectionValueList.stream().map(CollectionValueVO::getCollectionValue)
                .sorted().collect(Collectors.toList());

        List<DeliveryPlanPublishedCompareDayPO> insertList = new ArrayList<>();
        for (String day : dayList) {
            Date endDate = DateUtils.moveDay(startDateTruncateTime, Integer.parseInt(day) - 1);
            List<String> durList = getDurDayList(startDateTruncateTime, endDate);

            List<DeliveryPlanPublishedVO> newList = newDeliveryPlanPublishedVOS.stream()
                    .filter(newDeliveryPlanPublishedVO -> {
                        String date = DateUtils.dateToString(newDeliveryPlanPublishedVO.getDemandTime());
                        return durList.contains(date);
                    }).collect(Collectors.toList());
            Map<String, Integer> newMap = newList.stream().collect(Collectors.groupingBy(t ->
                            String.join("-",
                                    Optional.ofNullable(t.getOemCode()).orElse(""),
                                    Optional.ofNullable(t.getProductCode()).orElse("")),
                    Collectors.summingInt(DeliveryPlanPublishedVO::getDemandQuantity)));

            List<DeliveryPlanPublishedDTO> oldList = oldDataList.stream()
                    .filter(DeliveryPlanPublishedDTO -> {
                        String date = DateUtils.dateToString(DeliveryPlanPublishedDTO.getDemandTime());
                        return durList.contains(date);
                    }).collect(Collectors.toList());
            /**
             * 因为是按新发布数据维度进行统计的，老数据的时间范围可能是 2025-06-01~2025-06-30, 新数据时间范围是2025-06-02~2025-07-01,
             * 此时按新数据的开始时间算30天的时候，老数据其实只能取到29天的，在算变化率的时候时间范围要一直，所以用老数据的时间范围
             * （2025-06-02~2025-06-30）对新数据进行截断，确保时间范围一致再算变化率
             */
            List<String> oldDayList = oldList.stream().map(t -> DateUtils.dateToString(t.getDemandTime()))
                    .collect(Collectors.toList());
            List<DeliveryPlanPublishedVO> oldAndNewList = newDeliveryPlanPublishedVOS.stream()
                    .filter(newDeliveryPlanPublishedVO -> {
                        String date = DateUtils.dateToString(newDeliveryPlanPublishedVO.getDemandTime());
                        return oldDayList.contains(date);
                    }).collect(Collectors.toList());
            Map<String, Integer> oldAndNewMap = oldAndNewList.stream().collect(Collectors.groupingBy(t ->
                            String.join("-",
                                    Optional.ofNullable(t.getOemCode()).orElse(""),
                                    Optional.ofNullable(t.getProductCode()).orElse("")),
                    Collectors.summingInt(DeliveryPlanPublishedVO::getDemandQuantity)));

            Map<String, Integer> oldMap = oldList.stream().collect(Collectors.groupingBy(t ->
                            String.join("-",
                                    Optional.ofNullable(t.getOemCode()).orElse(""),
                                    Optional.ofNullable(t.getProductCode()).orElse("")),
                    Collectors.summingInt(DeliveryPlanPublishedDTO::getDemandQuantity)));
            for (Map.Entry<String, Integer> entry : newMap.entrySet()) {
                String[] split = entry.getKey().split("-");
                String oemCode = split[0];
                String productCode = split[1];
                Integer newValue = entry.getValue();
                Integer oldValue = oldMap.getOrDefault(entry.getKey(), 0);
                DeliveryPlanPublishedCompareDayPO po = new DeliveryPlanPublishedCompareDayPO();
                po.setId(UUIDUtil.getUUID());
                po.setOemCode(oemCode);
                po.setProductCode(productCode);
                po.setDayStr(day);
                po.setNewDemandQuantity(newValue);
                po.setOldDemandQuantity(oldValue);
                po.setVariableQuantity(newValue - oldValue);
                if (oldValue == 0) {
                    if (newValue == 0) {
                        po.setRateOfChange(BigDecimal.ZERO);
                    } else {
                        po.setRateOfChange(BigDecimal.ONE);
                    }
                } else {
                    Integer oldAndNewQty = oldAndNewMap.getOrDefault(entry.getKey(), 0);
                    int differenceValue = oldAndNewQty - oldValue;
                    po.setRateOfChange(new BigDecimal(differenceValue)
                            .divide(new BigDecimal(oldValue), 4, RoundingMode.HALF_UP));
                }
                insertList.add(po);
            }
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            BasePOUtils.insertBatchFiller(insertList);
            insertList.forEach(t -> {
                t.setPublisher(userId);
                t.setPublishTime(startDate);
                t.setCreator(userId);
                t.setModifier(userId);
            });
            deliveryPlanPublishedCompareDayDao.insertBatch(insertList);
        }
    }

    public static List<String> getDurDayList(Date startDate, Date endDate) {
        List<Date> intervalDates = DateUtils.getIntervalDates(startDate, endDate);
        return intervalDates.stream().map(DateUtils::dateToString).collect(Collectors.toList());
    }

    @Override
    public List<DeliveryPlanPublishedCompareDayVO2> deliveryPlanPublishedCompareDayView() {

        List<DeliveryPlanPublishedCompareDayVO2> result = new ArrayList<>();
        List<CollectionValueVO> collectionValueList = ipsFeign.getByCollectionCode("NUMBER_OF_DAYS_FOR_CHANGE_NOTICE_DISPLAY");
        if (CollectionUtils.isEmpty(collectionValueList)) {
            log.info("未维护字典表NUMBER_OF_DAYS_FOR_CHANGE_NOTICE_DISPLAY");
            return result;
        }
        List<String> dayStrList = collectionValueList.stream().map(CollectionValueVO::getCollectionValue).sorted().collect(Collectors.toList());
        List<DeliveryPlanPublishedCompareDayPO> deliveryPlanPublishedCompareDayPOList =
                deliveryPlanPublishedCompareDayDao.selectViewByDayStrList(dayStrList);
        if (CollectionUtils.isNotEmpty(deliveryPlanPublishedCompareDayPOList)) {
            List<String> productCodeList = deliveryPlanPublishedCompareDayPOList.stream()
                    .map(DeliveryPlanPublishedCompareDayPO::getProductCode)
                    .distinct().collect(Collectors.toList());
            List<NewProductStockPointVO> deliveryProductStockPoints = newMdsFeign.selectByProductCode(SystemHolder.getScenario(), productCodeList);
            Map<String, String> product2VehicleMap = deliveryProductStockPoints.stream()
                    .filter(t -> StringUtils.isNotEmpty(t.getVehicleModelCode()))
                    .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, NewProductStockPointVO::getVehicleModelCode, (v1, v2) -> v1));

            Map<String, List<DeliveryPlanPublishedCompareDayPO>> map = deliveryPlanPublishedCompareDayPOList
                    .stream().collect(Collectors.groupingBy(DeliveryPlanPublishedCompareDayPO::getProductCode));
            for (Map.Entry<String, List<DeliveryPlanPublishedCompareDayPO>> entry : map.entrySet()) {
                String productCode = entry.getKey();
                String vehicleModelCode = product2VehicleMap.get(productCode);
                DeliveryPlanPublishedCompareDayVO2 vo2 = new DeliveryPlanPublishedCompareDayVO2();
                vo2.setVehicleModelCode(vehicleModelCode);
                vo2.setProductCode(productCode);
                vo2.setPublishTime(entry.getValue().get(0).getPublishTime());
                vo2.setDynamicHeader(dayStrList);
                Map<String, List<DeliveryPlanPublishedCompareDayPO>> dayMap = entry.getValue()
                        .stream().collect(Collectors.groupingBy(DeliveryPlanPublishedCompareDayPO::getDayStr));
                for (String dayStr : dayStrList) {
                    List<DeliveryPlanPublishedCompareDayPO> deliveryPlanPublishedCompareDayPOS = dayMap.get(dayStr);
                    if (CollectionUtils.isNotEmpty(deliveryPlanPublishedCompareDayPOS)) {
                        int variableQuantity = deliveryPlanPublishedCompareDayPOS.stream().mapToInt(DeliveryPlanPublishedCompareDayPO::getVariableQuantity).sum();
                        vo2.getVariableQtyMap().put(dayStr, variableQuantity);
                        BigDecimal rateOfChange = BigDecimal.ZERO;
                        int oldDemandQuantity = deliveryPlanPublishedCompareDayPOS.stream().mapToInt(DeliveryPlanPublishedCompareDayPO::getOldDemandQuantity).sum();
                        if (oldDemandQuantity == 0) {
                            if (variableQuantity != 0) {
                                rateOfChange = BigDecimal.ONE;
                            }
                        } else {
                            rateOfChange = new BigDecimal(variableQuantity)
                                    .divide(new BigDecimal(oldDemandQuantity), 4, RoundingMode.HALF_UP);
                        }
                        vo2.getRateOfChangeMap().put(dayStr, rateOfChange);
                    } else {
                        vo2.getVariableQtyMap().put(dayStr, 0);
                        vo2.getRateOfChangeMap().put(dayStr, BigDecimal.ZERO);
                    }
                }
                boolean allMatch = vo2.getRateOfChangeMap().entrySet().stream().allMatch(t -> t.getValue()
                        .compareTo(BigDecimal.ZERO) == 0);
                if (allMatch) {
                    continue;
                }
                result.add(vo2);
            }
        }

        return result;
    }
}
