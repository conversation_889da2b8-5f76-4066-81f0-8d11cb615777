<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.report.infrastructure.dao.HighRiskProductOrderDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.report.vo.HighRiskProductOrderVO">
        <result column="version_id" jdbcType="VARCHAR" property="versionId"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
    </resultMap>
    <resultMap id="AggregationResultMap" extends="BaseResultMap"
               type="com.yhl.scp.dfp.report.vo.HighRiskProductOrderVO">
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="part_number" jdbcType="VARCHAR" property="partNumber"/>
        <result column="risk_level" jdbcType="VARCHAR" property="riskLevel"/>
    </resultMap>
    <resultMap id="StatisticsResultMap" extends="BaseResultMap"
               type="com.yhl.scp.dfp.report.vo.HighRiskProductOrderVO">
        <result column="demand_time" jdbcType="VARCHAR" property="demandTime"/>
        <result column="demand_quantity" jdbcType="DECIMAL" property="demandQuantity"/>
    </resultMap>
    <sql id="Base_Column_List">
        version_id,oem_code,product_code,vehicle_model_code
    </sql>
    <sql id="Aggregation_Column_List">
        <include refid="Base_Column_List" />,oem_name,product_name,stock_point_code,part_number,risk_level
    </sql>
    <sql id="Detail_Column_List">
        <include refid="Base_Column_List" />,demand_time,demand_quantity
    </sql>
    <select id="selectByVersionId" resultMap="AggregationResultMap">
        select
        <include refid="Aggregation_Column_List"/>
        from v_fdp_high_risk_product_order_aggregation
        where version_id = #{versionId,jdbcType=VARCHAR}
    </select>
    <select id="selectByCondition" resultMap="AggregationResultMap">
        select
        <include refid="Aggregation_Column_List"/>
        from v_fdp_high_risk_product_order_aggregation
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <select id="selectStatistics" resultMap="StatisticsResultMap">
        select
        <include refid="Detail_Column_List"/>
        from v_fdp_high_risk_product_order_detail
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
</mapper>