package com.yhl.scp.dfp.oem.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.ImmutableMap;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringConvertUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.deliverydockingorder.service.DeliveryDockingOrderDetailService;
import com.yhl.scp.dfp.oem.convertor.OemConvertor;
import com.yhl.scp.dfp.oem.domain.entity.OemDO;
import com.yhl.scp.dfp.oem.domain.service.OemDomainService;
import com.yhl.scp.dfp.oem.dto.OemDTO;
import com.yhl.scp.dfp.oem.enums.OemTallyOrderModeEnum;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemDao;
import com.yhl.scp.dfp.oem.infrastructure.po.OemPO;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.release.dto.ReleaseOemDTO;
import com.yhl.scp.dfp.release.vo.ReleaseOemVO;
import com.yhl.scp.dfp.sale.service.SaleOrganizeService;
import com.yhl.scp.dfp.sale.vo.SaleOrganizeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;

import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>OemServiceImpl</code>
 * <p>
 * 主机厂档案应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:48:08
 */
@Slf4j
@Service
public class OemServiceImpl extends AbstractService implements OemService {

    @Resource
    private OemDao oemDao;

    @Resource
    private OemDomainService oemDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private SaleOrganizeService saleOrganizeService;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private OemService oemService;
    
    @Resource
    private DeliveryDockingOrderDetailService deliveryDockingOrderDetailService;

    @Override
    public BaseResponse<Void> doCreate(OemDTO oemDTO) {
        // String customerCode = oemDTO.getCustomerCode();
        // String locationCode = oemDTO.getLocationCode();
        // String[] join = new String[]{customerCode, locationCode};
        // oemDTO.setOemCode(StringUtils.join(join, "_"));
        // 0.数据转换
        OemDO oemDO = OemConvertor.INSTANCE.dto2Do(oemDTO);
        OemPO oemPO = OemConvertor.INSTANCE.dto2Po(oemDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        oemDomainService.validation(oemDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(oemPO);
        oemDao.insertWithPrimaryKey(oemPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(OemDTO oemDTO) {
        String customerCode = oemDTO.getCustomerCode();
        String locationCode = oemDTO.getLocationCode();
        String[] join = new String[]{customerCode, locationCode};
        oemDTO.setOemCode(StringUtils.join(join, "_"));
        // 0.数据转换
        OemDO oemDO = OemConvertor.INSTANCE.dto2Do(oemDTO);
        OemPO oemPO = OemConvertor.INSTANCE.dto2Po(oemDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        oemDomainService.validation(oemDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(oemPO);
        oemDao.update(oemPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<OemDTO> list) {
        /* if (CollectionUtils.isNotEmpty(list)) {
            for (OemDTO oemDTO : list) {
                String customerCode = oemDTO.getCustomerCode();
                String locationCode = oemDTO.getLocationCode();
                String[] join = new String[]{customerCode, locationCode};
                oemDTO.setOemCode(StringUtils.join(join, "_"));
            }
        } */
        List<OemPO> newList = OemConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        oemDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<OemDTO> list) {
        /* if (CollectionUtils.isNotEmpty(list)) {
            for (OemDTO oemDTO : list) {
                String customerCode = oemDTO.getCustomerCode();
                String locationCode = oemDTO.getLocationCode();
                String[] join = new String[]{customerCode, locationCode};
                oemDTO.setOemCode(StringUtils.join(join, "_"));
            }
        } */
        List<OemPO> newList = OemConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        oemDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return oemDao.deleteBatch(idList);
        }
        return oemDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public OemVO selectByPrimaryKey(String id) {
        OemPO po = oemDao.selectByPrimaryKey(id);
        return OemConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mds_oem")
    public List<OemVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mds_oem")
    public List<OemVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<OemVO> dataList = oemDao.selectByCondition(sortParam, queryCriteriaParam);
        OemServiceImpl target = springBeanUtils.getBean(OemServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<OemVO> selectByParams(Map<String, Object> params) {
        List<OemPO> list = oemDao.selectByParams(params);
        return OemConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<OemVO> selectVOByConditionParams(Map<String, Object> params) {
        List<OemPO> list = oemDao.selectVOByConditionParams(params);
        return OemConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<LabelValue<String>> queryNameAndCode() {
        List<OemVO> oemVOS = this.selectAll();
        if (CollectionUtils.isNotEmpty(oemVOS)) {
            List<LabelValue<String>> list = oemVOS.stream()
                    .map(x -> new LabelValue<>(x.getOemName(), x.getOemCode()))
                    .collect(Collectors.toList());
            return list.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    lv -> Arrays.asList(lv.getLabel(), lv.getValue()),
                                    lv -> lv,
                                    (existing, replacement) -> existing
                            ),
                            map -> new ArrayList<>(map.values())
                    ));
        }
        return Collections.emptyList();
    }

    @Override
    public String getAddressByOemCode(String oemCode) {
        Map<String, Object> queryParam = new HashMap<>(2);
        queryParam.put("oemCode", oemCode);
        List<OemVO> oemVOS = oemService.selectByParams(queryParam);
        if (CollectionUtils.isEmpty(oemVOS)) {
            return "";
        }
        return oemVOS.get(0).getLocationArea2();
    }

    @Override
    public List<LabelValue<String>> getCustomerByNoEdi() {
        Map<String, Object> params = new HashMap<>();
        params.put("ediFlag", "NO");
        params.put("enabled", YesOrNoEnum.YES.getCode());
        List<OemVO> oemVOS = this.selectByParams(params);
        if (CollectionUtils.isNotEmpty(oemVOS)) {
            List<LabelValue<String>> list = oemVOS.stream()
                    .map(x -> new LabelValue<>(x.getCustomerName(), x.getCustomerCode()))
                    .collect(Collectors.toList());
            return list.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    lv -> Arrays.asList(lv.getLabel(), lv.getValue()),
                                    lv -> lv,
                                    (existing, replacement) -> existing
                            ),
                            map -> new ArrayList<>(map.values())
                    ));
        }
        return Collections.emptyList();
    }

    @Override
    public List<String> getAddressTwo(String locationArea2) {
        if (StringUtils.isBlank(locationArea2)) {
            return Collections.emptyList();
        }
        List<OemPO> list = oemDao.getAddressTwoLike(StringConvertUtils.convertToLike(locationArea2));
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<OemVO> oemVOS = OemConvertor.INSTANCE.po2Vos(list);
        if (CollectionUtils.isEmpty(oemVOS)) {
            return Collections.emptyList();
        }
        return oemVOS.stream().map(OemVO::getLocationArea2).collect(Collectors.toList());
    }

    @Override
    public List<OemVO> getAddressOneThreeFourByCustomer(String customerCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("customerCode", customerCode);
        List<OemVO> oemVOS = this.selectByParams(params);
        if (CollectionUtils.isNotEmpty(oemVOS)) {
            return oemVOS;
        }
        return Collections.emptyList();
    }

    @Override
    public String getOemCodeByCustomerCode(String customerCode) {
        if (customerCode == null || customerCode.isEmpty()) {
            throw new IllegalArgumentException("请传入客户编码再试");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("customerCode", customerCode);
        List<OemVO> oemVOS = this.selectByParams(params);
        if (CollectionUtils.isEmpty(oemVOS)) {
            return "";
        }
        // 获取已存在的oemCode前缀
        String oemCodePrefix = customerCode + "_";
        Map<String, Boolean> existingOemCodes = new HashMap<>();
        for (OemVO oemVO : oemVOS) {
            String oemCode = oemVO.getOemCode();
            if (oemCode.startsWith(oemCodePrefix)) {
                existingOemCodes.put(oemCode, true);
            }
        }
        int serialNumber = 1;
        String newOemCode;
        do {
            newOemCode = String.format("%s%03d", oemCodePrefix, serialNumber);
            serialNumber++;
        } while (existingOemCodes.containsKey(newOemCode));
        return newOemCode;
    }

    @Override
    public List<String> getPayment(String customerCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("customerCode", customerCode);
        List<OemVO> oemVOS = this.selectByParams(params);
        if (CollectionUtils.isNotEmpty(oemVOS)) {
            return oemVOS.stream().map(OemVO::getPaymentTerm).distinct().collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<OemVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public List<OemVO> getOemCodeByUserPermission() {
        return oemDao.selectByCondition(null, null);
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.OEM.getCode();
    }

    @Override
    public List<OemVO> invocation(List<OemVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public List<LabelValue<String>> queryOemInfo() {
        List<OemVO> oemVOS = oemDao.selectByCondition(null, null);
        return oemVOS.stream()
                .sorted(Comparator.comparing(OemVO::getCreateTime).reversed()).map(x -> {
                    LabelValue<String> labelValue = new LabelValue<>();
                    labelValue.setLabel(x.getOemName() + "(" + x.getOemCode() + ")");
                    labelValue.setValue(x.getOemCode());
                    return labelValue;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<LabelValue<String>> queryCustomerInfo() {
        return oemDao.selectCustomers(new HashMap<>(2)).stream()
                .map(x -> new LabelValue<>(x.getCustomerName(), x.getCustomerCode()))
                .sorted(Comparator.comparing(LabelValue::getLabel)).collect(Collectors.toList());
    }

    @Override
    public BaseResponse<Void> syncCustomers(String organizeId, String tenantId) {
        List<SaleOrganizeVO> saleOrganizeVOS = saleOrganizeService.selectAll();
        if (CollectionUtils.isEmpty(saleOrganizeVOS)) {
            return BaseResponse.error("销售组织为空");
        }
        saleOrganizeVOS.stream().filter(x -> YesOrNoEnum.YES.getCode().equals(x.getEnabled()))
                // 临时逻辑 wei.liu 2024/10/12 10:52:00
                .filter(x -> organizeId.equals(x.getId()))
                .forEach(x -> {
                    Map<String, Object> params = MapUtil.newHashMap();
                    params.put("ebsOuId", x.getId());
                    params.put("sitePurpose", "SHIP_TO");
                    // 调用远程的客户信息
                    newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.ERP.getCode(),
                            ApiCategoryEnum.CUSTOMER.getCode(), params);
                });
        return BaseResponse.success("同步成功");
    }

    /**
     * 根据客户编码获取主机厂编码
     *
     * @param customerCode
     * @return
     */
    @Override
    public List<LabelValue<String>> queryOemCodeByCustomerCode(String customerCode) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("customerCode", customerCode);
        queryMap.put("enabled", YesOrNoEnum.YES.getCode());
        List<OemVO> oemVOS = this.selectByParams(queryMap);
        return oemVOS.stream()
                .map(x -> new LabelValue<>(x.getOemName() + "(" + x.getOemCode() + ")", x.getOemCode()))
                .collect(Collectors.toList());
    }

    @Override
    public int deleteBatchVersion(List<RemoveVersionDTO> removeVersionDTOS) {
        if (CollectionUtils.isEmpty(removeVersionDTOS)) {
            return 0;
        }
        oemDomainService.checkDelete(removeVersionDTOS);
        return oemDao.deleteBatchVersion(removeVersionDTOS);
    }

    /**
     * 根据版本ID获取主机厂信息
     *
     * @param releaseOemDTO
     * @return
     */
    @Override
    public List<ReleaseOemVO> selectPageByVersionId(ReleaseOemDTO releaseOemDTO) {
        return oemDao.selectPageByVersionIdAndCondition(releaseOemDTO);
    }

	@Override
	public String getTransitClauseByCodes(String oemCode, String productCode) {
		List<String> oemCodeList = Arrays.asList(oemCode.split(","));
		List<OemVO> oemList = oemService.selectByParams(ImmutableMap.of(
				"enabled", YesOrNoEnum.YES.getCode(),
				"oemCodes", oemCodeList));
		if(CollectionUtils.isEmpty(oemList)) {
			return "";
		}
		//先校验产品理货单模式
		if(StringUtils.isNotEmpty(productCode)) {
			List<String> productCodeList = Arrays.asList(productCode.split(","));
			deliveryDockingOrderDetailService.getDeliveryDockingOrderTallyOrderMode(productCodeList, oemList);
		}else {
        	for (OemVO oemVO : oemList) {
    			if(StringUtils.isEmpty(oemVO.getTallyOrderMode())) {
    				throw new BusinessException("主机厂" + oemVO.getOemCode() + "理货单模式未维护");
    			}
    		}
    		for (OemVO oemVO : oemList) {
    			if(OemTallyOrderModeEnum.GRP.getCode().equals(oemVO.getTallyOrderMode()) && oemList.size() > 1) {
    				throw new BusinessException("主机厂" + oemVO.getOemCode() + "存在理货单模式为GRP");
    			}
    		}
		}
		return String.join(",", oemList.stream()
				.filter( e-> StringUtils.isNotEmpty(e.getTransitClause()))
				.map(OemVO::getTransitClause).distinct().collect(Collectors.toList()));
	}

    @Override
    public List<OemVO> selectProductEdiFlag(String ediFlag, String productEdiFlag) {
        return oemDao.selectProductEdiFlag(ediFlag, productEdiFlag);
    }

    @Override
    public List<OemVO> selectVOByParams(Map<String, Object> params) {
        return oemDao.selectVOByParams(params);
    }

	@Override
	public OemVO selectByOemCode(String oemCode) {
		List<OemVO> oemVOS = this.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), 
    			"oemCode" , oemCode));
		if(CollectionUtils.isEmpty(oemVOS)) {
			throw new BusinessException("未获取到主机厂数据信息");
		}
		return oemVOS.get(0);
	}

}
