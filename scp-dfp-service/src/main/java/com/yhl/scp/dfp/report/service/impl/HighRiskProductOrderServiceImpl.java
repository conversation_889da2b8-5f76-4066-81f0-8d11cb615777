package com.yhl.scp.dfp.report.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.dfp.report.infrastructure.dao.HighRiskProductOrderDao;
import com.yhl.scp.dfp.report.service.HighRiskProductOrderService;
import com.yhl.scp.dfp.report.vo.HighRiskProductOrderVO;
import com.yhl.scp.dfp.safety.service.SafetyStockLevelService;
import com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO;
import com.yhl.scp.dfp.stock.service.InventoryBatchDetailService;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.basic.routing.vo.StandardStepBasicVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>HighRiskProductOrderServiceImpl</code>
 * <p>
 * HighRiskProductOrderServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-14 23:20:59
 */
@Service
public class HighRiskProductOrderServiceImpl implements HighRiskProductOrderService {

    @Resource
    private HighRiskProductOrderDao highRiskProductOrderDao;

    @Resource
    private SafetyStockLevelService safetyStockLevelService;

    @Resource
    private InventoryBatchDetailService inventoryBatchDetailService;

    @Resource
    private MpsFeign mpsFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    public static final String SUB_INVENTORY_CPSJ = "CPSJ";

    public static final String PRE_OPERATION = "预处理";
    public static final String PAINTING_OPERATION = "印刷";
    public static final String FORMING_OPERATION = "成型";
    public static final String MERGING_OPERATION = "合片";
    public static final String PACKAGING_OPERATION = "包装";

    @Override
    public List<HighRiskProductOrderVO> selectByVersionId(String versionId) {
        return highRiskProductOrderDao.selectByVersionId(versionId);
    }

    @Override
    public List<HighRiskProductOrderVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam,
                                                     String organizationCode) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam, organizationCode);
    }

    @Override
    public List<HighRiskProductOrderVO> selectByCondition(String sortParam, String queryCriteriaParam,
                                                          String organizationCode) {
        String scenario = SystemHolder.getScenario();
        List<HighRiskProductOrderVO> dataList = highRiskProductOrderDao.selectByCondition(sortParam, queryCriteriaParam);
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        Map<String, List<HighRiskProductOrderVO>> detailGroup =
                highRiskProductOrderDao.selectStatistics(sortParam, queryCriteriaParam).stream()
                        .collect(Collectors.groupingBy(x -> String.join(Constants.DELIMITER,
                                x.getOemCode(), x.getProductCode(), x.getVehicleModelCode())));
        List<String> productCodes = dataList.stream().map(HighRiskProductOrderVO::getProductCode)
                .distinct().collect(Collectors.toList());

        List<String> oemCodes = dataList.stream().map(HighRiskProductOrderVO::getOemCode)
                .distinct().collect(Collectors.toList());
        List<SafetyStockLevelVO> safetyStockLevels = safetyStockLevelService.selectStandardStockDays(oemCodes);
        Map<String, String> oemCode2GnStockPointCodeMap = safetyStockLevels.stream()
                .filter(x -> StockPointTypeEnum.GN.getCode().equals(x.getStockPointType()))
                .collect(Collectors.toMap(SafetyStockLevelVO::getOemCode, SafetyStockLevelVO::getStockCode,
                        (v1, v2) -> v1));
        Map<String, BigDecimal> standardStockDayMap = safetyStockLevels.stream().collect(Collectors
                .toMap(x -> String.join(Constants.DELIMITER, x.getOemCode(), x.getStockPointType()),
                        SafetyStockLevelVO::getStandardStockDay, (v1, v2) -> v1));

        List<NewStockPointVO> newStockPoints = newMdsFeign.selectStockPointByParams(scenario,
                ImmutableMap.of("stockPointType", StockPointTypeEnum.BC.getCode()));
        List<String> saleOrganizations = newStockPoints.stream().filter(e ->
                        StringUtils.isNotEmpty(e.getOrganizeType())
                                && StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        List<String> productOrganizations = newStockPoints.stream().filter(e ->
                        StringUtils.isNotEmpty(e.getOrganizeType())
                                && StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());

        Map<String, String> standardStepMap = newMdsFeign.selectStandardStepAll(scenario).stream().collect(Collectors
                .toMap(p -> p.getStockPointCode() + p.getStandardStepName(),
                        StandardStepBasicVO::getStandardStepCode, (v1, v2) -> v1));

        List<InventoryBatchDetailVO> bcInventoryBatchDetails =
                inventoryBatchDetailService.selectByProductCodes(productCodes, StockPointTypeEnum.BC.getCode());
        Map<String, SubInventoryCargoLocationVO> cargoLocationMap = getCargoLocationMap(bcInventoryBatchDetails, scenario);
        Map<String, List<InventoryBatchDetailVO>> finishInventoryMap = bcInventoryBatchDetails.stream()
                .filter(t -> StringUtils.isEmpty(t.getOperationCode())
                        && saleOrganizations.contains(t.getStockPointCode())
                        && SUB_INVENTORY_CPSJ.equals(t.getSubinventory()))
                .collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));
        Map<String, List<InventoryBatchDetailVO>> semiFinishInventoryMap = bcInventoryBatchDetails.stream()
                .filter(t -> StringUtils.isEmpty(t.getOperationCode())
                        && productOrganizations.contains(t.getStockPointCode()))
                .collect(Collectors.groupingBy(p ->
                        String.join("-", p.getStockPointCode(), p.getProductCode())));
        Map<String, List<InventoryBatchDetailVO>> operationInventoryMap = bcInventoryBatchDetails.stream()
                .filter(t -> productOrganizations.contains(t.getStockPointCode()))
                .collect(Collectors.groupingBy(p -> String.join("-",
                        p.getStockPointCode(), p.getProductCode(), p.getOperationCode())));
        Map<String, BigDecimal> gnFgQuantityMap = inventoryBatchDetailService.selectByProductCodes(productCodes,
                StockPointTypeEnum.GN.getCode()).stream().filter(t ->
                StringUtils.isEmpty(t.getOperationCode())).collect(Collectors.groupingBy(x ->
                String.join(Constants.DELIMITER, x.getStockPointCode(), x.getProductCode()),
                Collectors.reducing(BigDecimal.ZERO, x ->
                                new BigDecimal(x.getCurrentQuantity()), BigDecimal::add)));
        for (HighRiskProductOrderVO item : dataList) {
            String oemCode = item.getOemCode();
            String productCode = item.getProductCode();
            String vehicleModelCode = item.getVehicleModelCode();
            String joinKey = String.join(Constants.DELIMITER, oemCode, productCode, vehicleModelCode);
            item.setDemand15Days(BigDecimal.ZERO);
            item.setDemand30Days(BigDecimal.ZERO);
            if (detailGroup.containsKey(joinKey)) {
                List<HighRiskProductOrderVO> sortedList = detailGroup.get(joinKey).stream().sorted(Comparator
                        .comparing(HighRiskProductOrderVO::getDemandTime)).collect(Collectors.toList());
                int size = sortedList.size();
                BigDecimal demand15Days = sortedList.subList(0, Math.min(size, 15)).stream()
                        .map(HighRiskProductOrderVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal demand30Days = sortedList.subList(0, Math.min(size, 30)).stream()
                        .map(HighRiskProductOrderVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                item.setDemand15Days(demand15Days);
                item.setDemand30Days(demand30Days);
            }
            // 厂外设定标准安全库存天数
            String outsideKey = String.join(Constants.DELIMITER, oemCode, StockPointTypeEnum.GN.getCode());
            if (standardStockDayMap.containsKey(outsideKey)) {
                item.setOutsideStandardSafetyStockDays(standardStockDayMap.get(outsideKey));
            }
            // 厂内设定标准安全库存天数
            String insideKey = String.join(Constants.DELIMITER, oemCode, StockPointTypeEnum.BC.getCode());
            if (standardStockDayMap.containsKey(outsideKey)) {
                item.setInsideStandardSafetyStockDays(standardStockDayMap.get(insideKey));
            }
            // 中转库成品库存
            if (oemCode2GnStockPointCodeMap.containsKey(oemCode)) {
                String gnStockPointCode = oemCode2GnStockPointCodeMap.get(oemCode);
                String gnFgKey = String.join(Constants.DELIMITER, gnStockPointCode, productCode);
                item.setTransferFgInventory(gnFgQuantityMap.getOrDefault(gnFgKey, null));
            }
            // String stockPointCode = item.getStockPointCode();
            BigDecimal ycl = getInventory(PRE_OPERATION, organizationCode, productCode, standardStepMap,
                    operationInventoryMap, cargoLocationMap);
            BigDecimal ys = getInventory(PAINTING_OPERATION, organizationCode, productCode, standardStepMap,
                    operationInventoryMap, cargoLocationMap);
            BigDecimal cx = getInventory(FORMING_OPERATION, organizationCode, productCode, standardStepMap,
                    operationInventoryMap, cargoLocationMap);
            BigDecimal hp = getInventory(MERGING_OPERATION, organizationCode, productCode, standardStepMap,
                    operationInventoryMap, cargoLocationMap);
            BigDecimal bz = getInventory(PACKAGING_OPERATION, organizationCode, productCode, standardStepMap,
                    operationInventoryMap, cargoLocationMap);

            assembleInventory(productCode, finishInventoryMap, cargoLocationMap, item);
            /*库存点-->货位-->半品*/
            // 中转库半品库存
            // item.setTransferSemiInventory();
            // 发运库库存* 待确认
            item.setShippingInventory(null);
            // 厂内成品库存量 SJ下的子库存CPSJ
            // item.setInsideFgInventory();
            // 半品库存
            item.setStepInventories(Lists.newArrayList(ycl, ys, cx, hp, bz));
            // 本票需求缺口* 待确认
            item.setDemandGap(null);
            // 标准单件包装量* 待确认
            item.setStandardPackagingQty(null);
        }
        return dataList;
    }

    public Map<String, SubInventoryCargoLocationVO> getCargoLocationMap(
            List<InventoryBatchDetailVO> inventoryBatchDetails, String scenario) {
        Map<String, SubInventoryCargoLocationVO> result = new HashMap<>();
        if (CollectionUtils.isEmpty(inventoryBatchDetails)) {
            return result;
        }
        List<String> spaceList = inventoryBatchDetails.stream().map(InventoryBatchDetailVO::getFreightSpace)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(spaceList)) {
            return result;
        }
        return mpsFeign.queryByFreightSpaces(scenario,
                spaceList, StockPointTypeEnum.BC.getCode()).stream().collect(Collectors
                .toMap(SubInventoryCargoLocationVO::getFreightSpaceCode,
                        Function.identity(), (v1, v2) -> v1));
    }

    private BigDecimal getInventory(String op, String stockPointCode, String productCode, Map<String, String> stepMap,
                                Map<String, List<InventoryBatchDetailVO>> inventoryMap,
                                Map<String, SubInventoryCargoLocationVO> cargoLocationMap) {
        if (Objects.isNull(stockPointCode)) {
            return BigDecimal.ZERO;
        }
        String stockPoint = "S1";
        String stockPoint2 = "S2";
        if (stockPoint.equals(stockPointCode) && op.equals(PAINTING_OPERATION)) {
            op = "镀膜";
        }
        if (stockPoint2.equals(stockPointCode) && op.equals(FORMING_OPERATION)) {
            op = "钢化";
        }
        // 工序代码
        String op1 = stepMap.get(stockPointCode + op);
        String opMainKey = String.join("-", stockPointCode, productCode, op1);
        String opMainKey1 = String.join("-", stockPointCode, productCode, "");
        String finalOp = op;
        List<InventoryBatchDetailVO> inventoryBatchDetails = inventoryMap.entrySet().stream()
                .filter(entry -> entry.getKey().equals(opMainKey)
                        || (PACKAGING_OPERATION.equals(finalOp) && entry.getKey().equals(opMainKey1)))
                .map(Map.Entry::getValue).flatMap(List::stream).filter(p -> {
                    String freightSpace = p.getFreightSpace();
                    SubInventoryCargoLocationVO subInventoryCargoLocation = cargoLocationMap.get(freightSpace);
                    return null != subInventoryCargoLocation;
                }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(inventoryBatchDetails)) {
            return BigDecimal.ZERO;
        }
        return inventoryBatchDetails.stream().filter(x ->
                StringUtils.isNotBlank(x.getCurrentQuantity())).map(x ->
                new BigDecimal(x.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private void assembleInventory(String productCode,
                                   Map<String, List<InventoryBatchDetailVO>> finishInventoryMap,
                                   Map<String, SubInventoryCargoLocationVO> cargoLocationMap,
                                   HighRiskProductOrderVO item) {
        if (finishInventoryMap.containsKey(productCode)) {
            // 维护产品编码对应的成品库存
            List<InventoryBatchDetailVO> finishList = getFinishInventory(finishInventoryMap.get(productCode),
                    cargoLocationMap);
            BigDecimal finishInventory = finishList.stream().map(t ->
                    new BigDecimal(t.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
            item.setInsideFgInventory(finishInventory);
        }
    }

    private List<InventoryBatchDetailVO> getFinishInventory(List<InventoryBatchDetailVO> inventoryBatchDetails,
                                                            Map<String, SubInventoryCargoLocationVO> cargoLocationMap) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(inventoryBatchDetails)) {
            return new ArrayList<>();
        }
        return inventoryBatchDetails.stream().filter(p -> {
            String freightSpace = p.getFreightSpace();
            SubInventoryCargoLocationVO subInventoryCargoLocation = cargoLocationMap.get(freightSpace);
            return null != subInventoryCargoLocation;
        }).collect(Collectors.toList());
    }

}