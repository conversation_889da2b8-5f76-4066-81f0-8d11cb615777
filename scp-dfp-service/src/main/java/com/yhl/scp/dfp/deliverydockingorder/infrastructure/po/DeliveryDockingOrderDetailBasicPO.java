package com.yhl.scp.dfp.deliverydockingorder.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>DeliveryDockingOrderDetailBasicPO</code>
 * <p>
 * 货对接单详情基础PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-29 21:04:46
 */
public class DeliveryDockingOrderDetailBasicPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 958240551665008772L;

    /**
     * 发货对接单号
     */
    private String deliveryDockingNumber;
    /**
     * 发货对接单行号
     */
    private String deliveryDockingLineNumber;
    /**
     * 主表id
     */
    private String deliveryDockingId;
    /**
     * 本厂编码
     */
    private String productCode;
    /**
     * 主机厂编码
     */
    private String oemCode;
    /**
     * 发货数量
     */
    private Integer deliveryQuantity;
    /**
     * 必发数量
     */
    private Integer mustQuantity;
    /**
     * 物料器具
     */
    private String materialEquipment;
    /**
     * 箱数
     */
    private String boxNumber;
    /**
     * 发货时间
     */
    private Date deliveryTime;
    /**
     * 毛重
     */
    private BigDecimal grossWeight;
    /**
     * 体积
     */
    private BigDecimal volume;
    /**
     * 柜型
     */
    private String cabinetType;
    /**
     * 提单号
     */
    private String billLadingNumber;
    /**
     * 发票号
     */
    private String invoiceNumber;
    /**
     * 实际发货数量
     */
    private Integer actualDeliveryQuantity;
    /**
     * 实际发货箱数
     */
    private Integer actualDeliveryBoxQuantity;
    /**
     * 状态
     */
    private String status;
    /**
     * 版本号
     */
    private String versionCode;
    /**
     * 需求来源id
     */
    private String demandSourcesId;
    /**
     * 数据源
     */
    private String dataSources;
    /**
     * 供应类型
     */
    private String businessType;
    /**
     * 贸易类型
     */
    private String marketType;
    /**
     * 运输方式
     */
    private String transportMode;
    /**
     * 版本
     */
    private Integer versionValue;
    
    /**
     * 是否需要冲销
     */
    private String writtenOffFlag;

    public String getDeliveryDockingNumber() {
        return deliveryDockingNumber;
    }

    public void setDeliveryDockingNumber(String deliveryDockingNumber) {
        this.deliveryDockingNumber = deliveryDockingNumber;
    }

    public String getDeliveryDockingLineNumber() {
        return deliveryDockingLineNumber;
    }

    public void setDeliveryDockingLineNumber(String deliveryDockingLineNumber) {
        this.deliveryDockingLineNumber = deliveryDockingLineNumber;
    }

    public String getDeliveryDockingId() {
        return deliveryDockingId;
    }

    public void setDeliveryDockingId(String deliveryDockingId) {
        this.deliveryDockingId = deliveryDockingId;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }

    public Integer getDeliveryQuantity() {
        return deliveryQuantity;
    }

    public void setDeliveryQuantity(Integer deliveryQuantity) {
        this.deliveryQuantity = deliveryQuantity;
    }

    public Integer getMustQuantity() {
        return mustQuantity;
    }

    public void setMustQuantity(Integer mustQuantity) {
        this.mustQuantity = mustQuantity;
    }

    public String getMaterialEquipment() {
        return materialEquipment;
    }

    public void setMaterialEquipment(String materialEquipment) {
        this.materialEquipment = materialEquipment;
    }

    public String getBoxNumber() {
        return boxNumber;
    }

    public void setBoxNumber(String boxNumber) {
        this.boxNumber = boxNumber;
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public BigDecimal getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    public String getCabinetType() {
        return cabinetType;
    }

    public void setCabinetType(String cabinetType) {
        this.cabinetType = cabinetType;
    }

    public String getBillLadingNumber() {
        return billLadingNumber;
    }

    public void setBillLadingNumber(String billLadingNumber) {
        this.billLadingNumber = billLadingNumber;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public Integer getActualDeliveryQuantity() {
        return actualDeliveryQuantity;
    }

    public void setActualDeliveryQuantity(Integer actualDeliveryQuantity) {
        this.actualDeliveryQuantity = actualDeliveryQuantity;
    }

    public Integer getActualDeliveryBoxQuantity() {
        return actualDeliveryBoxQuantity;
    }

    public void setActualDeliveryBoxQuantity(Integer actualDeliveryBoxQuantity) {
        this.actualDeliveryBoxQuantity = actualDeliveryBoxQuantity;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(String versionCode) {
        this.versionCode = versionCode;
    }

    public String getDemandSourcesId() {
        return demandSourcesId;
    }

    public void setDemandSourcesId(String demandSourcesId) {
        this.demandSourcesId = demandSourcesId;
    }

    public String getDataSources() {
        return dataSources;
    }

    public void setDataSources(String dataSources) {
        this.dataSources = dataSources;
    }

    public String getBusinessType() {
		return businessType;
	}

    public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

    public String getMarketType() {
		return marketType;
	}

    public void setMarketType(String marketType) {
		this.marketType = marketType;
	}

    public String getTransportMode() {
		return transportMode;
	}

    public void setTransportMode(String transportMode) {
		this.transportMode = transportMode;
	}

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

	public String getWrittenOffFlag() {
		return writtenOffFlag;
	}

	public void setWrittenOffFlag(String writtenOffFlag) {
		this.writtenOffFlag = writtenOffFlag;
	}

}
