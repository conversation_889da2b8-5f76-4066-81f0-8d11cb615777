<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.massProduction.infrastructure.dao.MassProductionHandoverDetailDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.massProduction.infrastructure.po.MassProductionHandoverDetailPO">
        <!--@Table fdp_mass_production_handover_detail-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="mass_production_handover_id" jdbcType="VARCHAR" property="massProductionHandoverId"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="part_number" jdbcType="VARCHAR" property="partNumber"/>
        <result column="loading_position_sub" jdbcType="VARCHAR" property="loadingPositionSub"/>
        <result column="box_type" jdbcType="VARCHAR" property="boxType"/>
        <result column="piece_weight" jdbcType="VARCHAR" property="pieceWeight"/>
        <result column="box_weight" jdbcType="VARCHAR" property="boxWeight"/>
        <result column="box_spec" jdbcType="INTEGER" property="boxSpec"/>
        <result column="product_length" jdbcType="VARCHAR" property="productLength"/>
        <result column="product_width" jdbcType="VARCHAR" property="productWidth"/>
        <result column="product_thickness" jdbcType="VARCHAR" property="productThickness"/>
        <result column="product_sop" jdbcType="TIMESTAMP" property="productSop"/>
        <result column="product_eop" jdbcType="TIMESTAMP" property="productEop"/>
        <result column="part_record" jdbcType="VARCHAR" property="partRecord"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="update_order_planner_flag" jdbcType="VARCHAR" property="updateOrderPlannerFlag"/>
        <result column="produce_type" jdbcType="VARCHAR" property="produceType"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.massProduction.vo.MassProductionHandoverDetailVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,mass_production_handover_id,product_code,product_name,part_number,loading_position_sub,box_type,piece_weight,box_weight,box_spec,product_length,product_width,product_thickness,product_sop,product_eop,part_record,remark,enabled,creator,create_time,modifier,modify_time,version_value,
        update_order_planner_flag, produce_type
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.massProductionHandoverId != null and params.massProductionHandoverId != ''">
                and mass_production_handover_id = #{params.massProductionHandoverId,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productName != null and params.productName != ''">
                and product_name = #{params.productName,jdbcType=VARCHAR}
            </if>
            <if test="params.partNumber != null and params.partNumber != ''">
                and part_number = #{params.partNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.loadingPositionSub != null and params.loadingPositionSub != ''">
                and loading_position_sub = #{params.loadingPositionSub,jdbcType=VARCHAR}
            </if>
            <if test="params.boxType != null and params.boxType != ''">
                and box_type = #{params.boxType,jdbcType=VARCHAR}
            </if>
            <if test="params.pieceWeight != null">
                and piece_weight = #{params.pieceWeight,jdbcType=VARCHAR}
            </if>
            <if test="params.boxWeight != null">
                and box_weight = #{params.boxWeight,jdbcType=VARCHAR}
            </if>
            <if test="params.boxSpec != null">
                and box_spec = #{params.boxSpec,jdbcType=INTEGER}
            </if>
            <if test="params.productLength != null">
                and product_length = #{params.productLength,jdbcType=VARCHAR}
            </if>
            <if test="params.productWidth != null">
                and product_width = #{params.productWidth,jdbcType=VARCHAR}
            </if>
            <if test="params.productThickness != null">
                and product_thickness = #{params.productThickness,jdbcType=VARCHAR}
            </if>
            <if test="params.productSop != null">
                and product_sop = #{params.productSop,jdbcType=TIMESTAMP}
            </if>
            <if test="params.productEop != null">
                and product_eop = #{params.productEop,jdbcType=TIMESTAMP}
            </if>
            <if test="params.partRecord != null and params.partRecord != ''">
                and part_record = #{params.partRecord,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.updateOrderPlannerFlag != null and params.updateOrderPlannerFlag != ''">
                and update_order_planner_flag = #{params.updateOrderPlannerFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.produceType != null and params.produceType != ''">
                and produce_type = #{params.produceType,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_mass_production_handover_detail
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_mass_production_handover_detail
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from fdp_mass_production_handover_detail
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_mass_production_handover_detail
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from fdp_mass_production_handover_detail
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增（带主键） -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.massProduction.infrastructure.po.MassProductionHandoverDetailPO">
        insert into fdp_mass_production_handover_detail(
        id,
        mass_production_handover_id,
        product_code,
        product_name,
        part_number,
        loading_position_sub,
        box_type,
        piece_weight,
        box_weight,
        box_spec,
        product_length,
        product_width,
        product_thickness,
        product_sop,
        product_eop,
        part_record,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        update_order_planner_flag,
        produce_type)
        values (
        #{id,jdbcType=VARCHAR},
        #{massProductionHandoverId,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{partNumber,jdbcType=VARCHAR},
        #{loadingPositionSub,jdbcType=VARCHAR},
        #{boxType,jdbcType=VARCHAR},
        #{pieceWeight,jdbcType=VARCHAR},
        #{boxWeight,jdbcType=VARCHAR},
        #{boxSpec,jdbcType=INTEGER},
        #{productLength,jdbcType=VARCHAR},
        #{productWidth,jdbcType=VARCHAR},
        #{productThickness,jdbcType=VARCHAR},
        #{productSop,jdbcType=TIMESTAMP},
        #{productEop,jdbcType=TIMESTAMP},
        #{partRecord,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{updateOrderPlannerFlag,jdbcType=VARCHAR},
        #{produceType,jdbcType=VARCHAR})
    </insert>
    
    <!-- 批量新增（带主键） -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_mass_production_handover_detail(
        id,
        mass_production_handover_id,
        product_code,
        product_name,
        part_number,
        loading_position_sub,
        box_type,
        piece_weight,
        box_weight,
        box_spec,
        product_length,
        product_width,
        product_thickness,
        product_sop,
        product_eop,
        part_record,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        update_order_planner_flag,
        produce_type)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.massProductionHandoverId,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR},
        #{entity.productName,jdbcType=VARCHAR},
        #{entity.partNumber,jdbcType=VARCHAR},
        #{entity.loadingPositionSub,jdbcType=VARCHAR},
        #{entity.boxType,jdbcType=VARCHAR},
        #{entity.pieceWeight,jdbcType=VARCHAR},
        #{entity.boxWeight,jdbcType=VARCHAR},
        #{entity.boxSpec,jdbcType=INTEGER},
        #{entity.productLength,jdbcType=VARCHAR},
        #{entity.productWidth,jdbcType=VARCHAR},
        #{entity.productThickness,jdbcType=VARCHAR},
        #{entity.productSop,jdbcType=TIMESTAMP},
        #{entity.productEop,jdbcType=TIMESTAMP},
        #{entity.partRecord,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.updateOrderPlannerFlag,jdbcType=VARCHAR},
        #{entity.produceType,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.massProduction.infrastructure.po.MassProductionHandoverDetailPO">
        update fdp_mass_production_handover_detail set
        mass_production_handover_id = #{massProductionHandoverId,jdbcType=VARCHAR},
        product_code = #{productCode,jdbcType=VARCHAR},
        product_name = #{productName,jdbcType=VARCHAR},
        part_number = #{partNumber,jdbcType=VARCHAR},
        loading_position_sub = #{loadingPositionSub,jdbcType=VARCHAR},
        box_type = #{boxType,jdbcType=VARCHAR},
        piece_weight = #{pieceWeight,jdbcType=VARCHAR},
        box_weight = #{boxWeight,jdbcType=VARCHAR},
        box_spec = #{boxSpec,jdbcType=INTEGER},
        product_length = #{productLength,jdbcType=VARCHAR},
        product_width = #{productWidth,jdbcType=VARCHAR},
        product_thickness = #{productThickness,jdbcType=VARCHAR},
        product_sop = #{productSop,jdbcType=TIMESTAMP},
        product_eop = #{productEop,jdbcType=TIMESTAMP},
        part_record = #{partRecord,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER},
        update_order_planner_flag = #{updateOrderPlannerFlag,jdbcType=VARCHAR},
        produce_type = #{produceType,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.massProduction.infrastructure.po.MassProductionHandoverDetailPO">
        update fdp_mass_production_handover_detail
        <set>
            <if test="item.massProductionHandoverId != null and item.massProductionHandoverId != ''">
                mass_production_handover_id = #{item.massProductionHandoverId,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productName != null and item.productName != ''">
                product_name = #{item.productName,jdbcType=VARCHAR},
            </if>
            <if test="item.partNumber != null and item.partNumber != ''">
                part_number = #{item.partNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.loadingPositionSub != null and item.loadingPositionSub != ''">
                loading_position_sub = #{item.loadingPositionSub,jdbcType=VARCHAR},
            </if>
            <if test="item.boxType != null and item.boxType != ''">
                box_type = #{item.boxType,jdbcType=VARCHAR},
            </if>
            <if test="item.pieceWeight != null">
                piece_weight = #{item.pieceWeight,jdbcType=VARCHAR},
            </if>
            <if test="item.boxWeight != null">
                box_weight = #{item.boxWeight,jdbcType=VARCHAR},
            </if>
            <if test="item.boxSpec != null">
                box_spec = #{item.boxSpec,jdbcType=INTEGER},
            </if>
            <if test="item.productLength != null">
                product_length = #{item.productLength,jdbcType=VARCHAR},
            </if>
            <if test="item.productWidth != null">
                product_width = #{item.productWidth,jdbcType=VARCHAR},
            </if>
            <if test="item.productThickness != null">
                product_thickness = #{item.productThickness,jdbcType=VARCHAR},
            </if>
            <if test="item.productSop != null">
                product_sop = #{item.productSop,jdbcType=TIMESTAMP},
            </if>
            <if test="item.productEop != null">
                product_eop = #{item.productEop,jdbcType=TIMESTAMP},
            </if>
            <if test="item.partRecord != null and item.partRecord != ''">
                part_record = #{item.partRecord,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.updateOrderPlannerFlag != null and item.updateOrderPlannerFlag != ''">
                update_order_planner_flag = #{item.updateOrderPlannerFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.produceType != null and item.produceType != ''">
                produce_type = #{item.produceType,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_mass_production_handover_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="mass_production_handover_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.massProductionHandoverId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="part_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.partNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="loading_position_sub = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.loadingPositionSub,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="box_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.boxType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="piece_weight = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.pieceWeight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="box_weight = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.boxWeight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="box_spec = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.boxSpec,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="product_length = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productLength,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_width = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productWidth,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_thickness = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productThickness,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_sop = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productSop,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="product_eop = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productEop,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="part_record = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.partRecord,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="update_order_planner_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.updateOrderPlannerFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="produce_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.produceType,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update fdp_mass_production_handover_detail 
        <set>
            <if test="item.massProductionHandoverId != null and item.massProductionHandoverId != ''">
                mass_production_handover_id = #{item.massProductionHandoverId,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productName != null and item.productName != ''">
                product_name = #{item.productName,jdbcType=VARCHAR},
            </if>
            <if test="item.partNumber != null and item.partNumber != ''">
                part_number = #{item.partNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.loadingPositionSub != null and item.loadingPositionSub != ''">
                loading_position_sub = #{item.loadingPositionSub,jdbcType=VARCHAR},
            </if>
            <if test="item.boxType != null and item.boxType != ''">
                box_type = #{item.boxType,jdbcType=VARCHAR},
            </if>
            <if test="item.pieceWeight != null">
                piece_weight = #{item.pieceWeight,jdbcType=VARCHAR},
            </if>
            <if test="item.boxWeight != null">
                box_weight = #{item.boxWeight,jdbcType=VARCHAR},
            </if>
            <if test="item.boxSpec != null">
                box_spec = #{item.boxSpec,jdbcType=INTEGER},
            </if>
            <if test="item.productLength != null">
                product_length = #{item.productLength,jdbcType=VARCHAR},
            </if>
            <if test="item.productWidth != null">
                product_width = #{item.productWidth,jdbcType=VARCHAR},
            </if>
            <if test="item.productThickness != null">
                product_thickness = #{item.productThickness,jdbcType=VARCHAR},
            </if>
            <if test="item.productSop != null">
                product_sop = #{item.productSop,jdbcType=TIMESTAMP},
            </if>
            <if test="item.productEop != null">
                product_eop = #{item.productEop,jdbcType=TIMESTAMP},
            </if>
            <if test="item.partRecord != null and item.partRecord != ''">
                part_record = #{item.partRecord,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.updateOrderPlannerFlag != null and item.updateOrderPlannerFlag != ''">
                update_order_planner_flag = #{item.updateOrderPlannerFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.produceType != null and item.produceType != ''">
                produce_type = #{item.produceType,jdbcType=VARCHAR},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from fdp_mass_production_handover_detail where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_mass_production_handover_detail where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    
    <!-- 删除 -->
    <delete id="deleteByMassProductionHandoverId" parameterType="java.lang.String">
        delete from 
        	fdp_mass_production_handover_detail 
        where 
        	mass_production_handover_id = #{massProductionHandoverId,jdbcType=VARCHAR}
    </delete>
    
    <!-- 删除 -->
    <delete id="doDeleteByMassProductionHandoverIds">
        delete from 
        	fdp_mass_production_handover_detail 
        where 
        	mass_production_handover_id in
            <foreach collection="massProductionHandoverIds" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
    </delete>
</mapper>
