package com.yhl.scp.dfp.massProduction.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MassProductionHandoverDetailPO</code>
 * <p>
 * 量产移交信息详情表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 16:20:22
 */
public class MassProductionHandoverDetailPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 852506597761377135L;

        /**
     * 主表id
     */
        private String massProductionHandoverId;
        /**
     * 本厂编码
     */
        private String productCode;
        /**
     * 物料名称
     */
        private String productName;
        /**
     * 零件号
     */
        private String partNumber;
        /**
     * 装车位置小类
     */
        private String loadingPositionSub;
        /**
     * 包装方式
     */
        private String boxType;
        /**
     * 单片玻璃重量
     */
        private BigDecimal pieceWeight;
        /**
     * 料箱立项重量
     */
        private BigDecimal boxWeight;
        /**
     * 包装箱规格(成箱片数)
     */
        private Integer boxSpec;
        /**
     * 长
     */
        private BigDecimal productLength;
        /**
     * 宽
     */
        private BigDecimal productWidth;
        /**
     * 厚
     */
        private BigDecimal productThickness;
        /**
     * SOP
     */
        private Date productSop;
        /**
     * EOP
     */
        private Date productEop;
        /**
     * 零件履历
     */
        private String partRecord;
        /**
     * 版本
     */
        private Integer versionValue;
        
    /**
     * 是否修改人员权限
     */
    private String updateOrderPlannerFlag;
    
    /**
     * 开发类型
     */
    private String produceType;

    public String getMassProductionHandoverId() {
        return massProductionHandoverId;
    }

    public void setMassProductionHandoverId(String massProductionHandoverId) {
        this.massProductionHandoverId = massProductionHandoverId;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getPartNumber() {
        return partNumber;
    }

    public void setPartNumber(String partNumber) {
        this.partNumber = partNumber;
    }

    public String getLoadingPositionSub() {
        return loadingPositionSub;
    }

    public void setLoadingPositionSub(String loadingPositionSub) {
        this.loadingPositionSub = loadingPositionSub;
    }

    public String getBoxType() {
        return boxType;
    }

    public void setBoxType(String boxType) {
        this.boxType = boxType;
    }

    public BigDecimal getPieceWeight() {
        return pieceWeight;
    }

    public void setPieceWeight(BigDecimal pieceWeight) {
        this.pieceWeight = pieceWeight;
    }

    public BigDecimal getBoxWeight() {
        return boxWeight;
    }

    public void setBoxWeight(BigDecimal boxWeight) {
        this.boxWeight = boxWeight;
    }

    public Integer getBoxSpec() {
        return boxSpec;
    }

    public void setBoxSpec(Integer boxSpec) {
        this.boxSpec = boxSpec;
    }

    public BigDecimal getProductLength() {
        return productLength;
    }

    public void setProductLength(BigDecimal productLength) {
        this.productLength = productLength;
    }

    public BigDecimal getProductWidth() {
        return productWidth;
    }

    public void setProductWidth(BigDecimal productWidth) {
        this.productWidth = productWidth;
    }

    public BigDecimal getProductThickness() {
        return productThickness;
    }

    public void setProductThickness(BigDecimal productThickness) {
        this.productThickness = productThickness;
    }

    public Date getProductSop() {
        return productSop;
    }

    public void setProductSop(Date productSop) {
        this.productSop = productSop;
    }

    public Date getProductEop() {
        return productEop;
    }

    public void setProductEop(Date productEop) {
        this.productEop = productEop;
    }

    public String getPartRecord() {
        return partRecord;
    }

    public void setPartRecord(String partRecord) {
        this.partRecord = partRecord;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

	public String getUpdateOrderPlannerFlag() {
		return updateOrderPlannerFlag;
	}

	public void setUpdateOrderPlannerFlag(String updateOrderPlannerFlag) {
		this.updateOrderPlannerFlag = updateOrderPlannerFlag;
	}

	public String getProduceType() {
		return produceType;
	}

	public void setProduceType(String produceType) {
		this.produceType = produceType;
	}

}
