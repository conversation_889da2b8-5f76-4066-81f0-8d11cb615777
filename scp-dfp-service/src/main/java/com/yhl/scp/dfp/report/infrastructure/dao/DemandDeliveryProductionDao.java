package com.yhl.scp.dfp.report.infrastructure.dao;

import com.yhl.scp.dfp.report.vo.DemandDeliveryProductionVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>DemandDeliveryProductionDao</code>
 * <p>
 * 需求发货生产报表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-30
 */
public interface DemandDeliveryProductionDao {
    
    /**
     * 根据条件查询
     */
    List<DemandDeliveryProductionVO> selectByCondition(@Param("sortParam") String sortParam,
                                                       @Param("queryCriteriaParam") String queryCriteriaParam);
    
    /**
     * 分页查询需求发货生产报表数据
     */
    List<DemandDeliveryProductionVO> selectByPageWithParams(@Param("params") Map<String, Object> params);
    
    /**
     * 查询需求发货生产报表数据总数
     */
    long countByParams(@Param("params") Map<String, Object> params);
}
