package com.yhl.scp.dfp.deliverydockingorder.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>DeliveryDockingOrderDetailDO</code>
 * <p>
 * 发货对接单详情DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-29 13:58:17
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryDockingOrderDetailDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 372368758375455689L;

    /**
     * 主键id
     */
    private String id;
    /**
     * 发货对接单号
     */
    private String deliveryDockingNumber;
    /**
     * 发货对接单行号
     */
    private String deliveryDockingLineNumber;
    /**
     * 主表id
     */
    private String deliveryDockingId;
    /**
     * 本厂编码
     */
    private String productCode;
    /**
     * 主机厂编码
     */
    private String oemCode;
    /**
     * 发货数量
     */
    private Integer deliveryQuantity;
    /**
     * 必发数量
     */
    private Integer mustQuantity;
    /**
     * 物料器具
     */
    private String materialEquipment;
    /**
     * 箱数
     */
    private String boxNumber;
    /**
     * 发货时间
     */
    private Date deliveryTime;
    /**
     * 毛重
     */
    private BigDecimal grossWeight;
    /**
     * 体积
     */
    private BigDecimal volume;
    /**
     * 柜型
     */
    private String cabinetType;
    /**
     * 提单号
     */
    private String billLadingNumber;
    /**
     * 发票号
     */
    private String invoiceNumber;
    /**
     * 实际发货数量
     */
    private Integer actualDeliveryQuantity;
    /**
     * 实际发货箱数
     */
    private Integer actualDeliveryBoxQuantity;
    /**
     * 状态
     */
    private String status;
    /**
     * 版本号
     */
    private String versionCode;
    /**
     * 需求来源id
     */
    private String demandSourcesId;
    /**
     * 数据源
     */
    private String dataSources;
    /**
     * 供应类型
     */
    private String businessType;
    /**
     * 贸易类型
     */
    private String marketType;
    /**
     * 版本
     */
    private Integer versionValue;
    
    /**
     * 是否需要冲销
     */
    private String writtenOffFlag;

}
