package com.yhl.scp.dfp.feign.controller;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpCustomer;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpInventoryBatchDetail;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpSaleOrganize;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.grp.GrpEdiDeliveryDetail;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesInventoryBatchDetail;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesTransportRouting;
import com.yhl.scp.dfp.calendar.dto.ResourceCalendarRangeDTO;
import com.yhl.scp.dfp.calendar.service.DfpResourceCalendarService;
import com.yhl.scp.dfp.calendar.vo.WorkHourStatisticsVO;
import com.yhl.scp.dfp.carrier.dto.CarrierDataDTO;
import com.yhl.scp.dfp.carrier.service.CarrierDataService;
import com.yhl.scp.dfp.clean.service.CleanDemandDataDetailService;
import com.yhl.scp.dfp.clean.service.CleanDemandDataService;
import com.yhl.scp.dfp.clean.service.CleanForecastDataDetailService;
import com.yhl.scp.dfp.clean.service.CleanForecastDataService;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataDetailVO;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataVO;
import com.yhl.scp.dfp.clean.vo.CleanForecastDataDetailVO;
import com.yhl.scp.dfp.clean.vo.CleanForecastDataVO;
import com.yhl.scp.dfp.consignmentProduct.dto.ConsignmentProductDTO;
import com.yhl.scp.dfp.consignmentProduct.service.ConsignmentProductService;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastVersionDao;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataDetailService;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataService;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastVersionService;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanPublishedCompareService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanPublishedService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanVersionService;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVersionVO;
import com.yhl.scp.dfp.deliverydockingorder.dto.DeliveryDockingOrderApiDTO;
import com.yhl.scp.dfp.deliverydockingorder.service.DeliveryDockingOrderService;
import com.yhl.scp.dfp.demand.service.DemandVersionService;
import com.yhl.scp.dfp.demand.vo.DemandVersionVO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionService;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionVO;
import com.yhl.scp.dfp.massProduction.dto.MassProductionHandoverDTO;
import com.yhl.scp.dfp.massProduction.service.MassProductionHandoverService;
import com.yhl.scp.dfp.material.service.PartRiskLevelService;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.dfp.newProduct.convertor.NewProductTrialSubmissionDetailConvertor;
import com.yhl.scp.dfp.newProduct.dto.NewProductTrialSubmissionDTO;
import com.yhl.scp.dfp.newProduct.infrastructure.dao.NewProductTrialSubmissionDetailDao;
import com.yhl.scp.dfp.newProduct.service.NewProductTrialSubmissionDetailService;
import com.yhl.scp.dfp.newProduct.service.NewProductTrialSubmissionService;
import com.yhl.scp.dfp.newProduct.vo.NewProductTrialSubmissionDetailVO;
import com.yhl.scp.dfp.oem.convertor.OemConvertor;
import com.yhl.scp.dfp.oem.dto.OemAddressInventoryLogDTO;
import com.yhl.scp.dfp.oem.dto.OemDTO;
import com.yhl.scp.dfp.oem.service.*;
import com.yhl.scp.dfp.oem.vo.OemRiskLevelVO;
import com.yhl.scp.dfp.oem.vo.OemStockPointMapVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelVO;
import com.yhl.scp.dfp.origin.service.OriginDemandVersionService;
import com.yhl.scp.dfp.originDemand.dto.FdpOriginDemandForecastInterfaceLogDTO;
import com.yhl.scp.dfp.originDemand.dto.FdpOriginDemandInterfaceLogDTO;
import com.yhl.scp.dfp.originDemand.service.FdpOriginDemandForecastInterfaceLogService;
import com.yhl.scp.dfp.originDemand.service.FdpOriginDemandInterfaceLogService;
import com.yhl.scp.dfp.part.service.PartRelationMapService;
import com.yhl.scp.dfp.part.vo.PartRelationMapVO;
import com.yhl.scp.dfp.passenger.service.PassengerCarSaleService;
import com.yhl.scp.dfp.passenger.vo.PassengerCarSaleVO;
import com.yhl.scp.dfp.safety.service.SafetyStockLevelService;
import com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO;
import com.yhl.scp.dfp.sale.dto.SaleOrganizeDTO;
import com.yhl.scp.dfp.sale.service.SaleOrganizeService;
import com.yhl.scp.dfp.sale.vo.SaleOrganizeVO;
import com.yhl.scp.dfp.stock.dto.InventoryBatchDetailDTO;
import com.yhl.scp.dfp.stock.dto.InventoryDataDTO;
import com.yhl.scp.dfp.stock.service.InventoryBatchDetailService;
import com.yhl.scp.dfp.stock.service.InventoryRealTimeDataService;
import com.yhl.scp.dfp.stock.service.InventoryShiftService;
import com.yhl.scp.dfp.stock.service.OriginalFilmInTransitService;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.dfp.stock.vo.InventoryRealTimeDataVO;
import com.yhl.scp.dfp.stock.vo.InventoryShiftVO;
import com.yhl.scp.dfp.stock.vo.OriginalFilmInTransitVO;
import com.yhl.scp.dfp.transport.service.TransportRoutingInterfaceLogService;
import com.yhl.scp.dfp.transport.service.TransportRoutingService;
import com.yhl.scp.dfp.warehouse.dto.EnRouteDTO;
import com.yhl.scp.dfp.warehouse.dto.WarehouseReleaseRecordDTO;
import com.yhl.scp.dfp.warehouse.dto.WarehouseReleaseRecordLogDTO;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseRecordLogService;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseRecordService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>DfpFeignController</code>
 * <p>
 * DfpFeignController
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-22 14:40:17
 */
@Slf4j
@Api(tags = "Feign")
@RestController
public class DfpFeignController implements DfpFeign {

    @Resource
    private DeliveryPlanService deliveryPlanService;

    @Resource
    private InventoryRealTimeDataService inventoryRealTimeDataService;

    @Resource
    private ConsistenceDemandForecastDataService consistenceDemandForecastDataService;

    @Resource
    private PartRiskLevelService partRiskLevelService;

    @Resource
    private SafetyStockLevelService safetyStockLevelService;

    @Resource
    private LoadingDemandSubmissionService loadingDemandSubmissionService;

    @Resource
    private SaleOrganizeService saleOrganizeService;

    @Resource
    private OemService oemService;

    @Resource
    private ConsistenceDemandForecastDataDetailService consistenceDemandForecastDataDetailService;

    @Resource
    private DfpResourceCalendarService dfpResourceCalendarService;

    @Resource
    private InventoryBatchDetailService inventoryBatchDetailService;

    @Resource
    private PartRelationMapService partRelationMapService;

    @Resource
    private NewProductTrialSubmissionService newProductTrialSubmissionService;
    @Resource
    private NewMdsFeign mdsFeign;

    @Resource
    private DeliveryPlanService deliveryPlanDetailService;
    @Resource
    private OemStockPointMapService oemStockPointMapService;

    @Resource
    private ConsistenceDemandForecastVersionDao consistenceDemandForecastVersionDao;

    @Resource
    private WarehouseReleaseRecordService warehouseReleaseRecordService;

    @Resource
    private DeliveryPlanVersionService deliveryPlanVersionService;
    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private CleanDemandDataService cleanDemandDataService;

    @Resource
    private CleanDemandDataDetailService cleanDemandDataDetailService;

    @Resource NewMdsFeign newMdsFeign;

    @Resource
    TransportRoutingService transportRoutingService;

    @Resource
    OemAddressInventoryLogService oemAddressInventoryLogService;

    @Resource
    private TransportRoutingInterfaceLogService transportRoutingInterfaceLogService;


    @Resource
    private WarehouseReleaseRecordLogService warehouseReleaseRecordLogService;
    @Resource
    private OemVehicleModelService OemVehicleModelService;

    @Resource
    private FdpOriginDemandForecastInterfaceLogService originDemandForecastInterfaceLogService;
    @Resource
    private FdpOriginDemandInterfaceLogService originDemandInterfaceLogService;
    @Resource
    private DeliveryPlanPublishedService deliveryPlanPublishedService;

    @Resource
    private PassengerCarSaleService passengerCarSaleService;

    @Resource
    private OriginalFilmInTransitService originalFilmInTransitService;

    @Resource
    private ConsignmentProductService consignmentProductService;

    @Resource
    private DeliveryDockingOrderService deliveryDockingOrderService;

    @Resource
    private OriginDemandVersionService originDemandVersionService;

    @Resource
    private CarrierDataService carrierDataService;
    @Resource
    private DeliveryPlanPublishedCompareService deliveryPlanPublishedCompareService;

    @Resource
    private InventoryShiftService inventoryShiftService;

    @Resource
    private NewProductTrialSubmissionDetailDao newProductTrialSubmissionDetailDao;

    @Resource
    private NewProductTrialSubmissionDetailService newProductTrialSubmissionDetailService;

    @Resource
    private ConsistenceDemandForecastVersionService consistenceDemandForecastVersionService;

    @Resource
    private OemRiskLevelService oemRiskLevelService;

    @Resource
    private CleanForecastDataService cleanForecastDataService;

    @Resource
    private CleanForecastDataDetailService cleanForecastDataDetailService;

    @Resource
    private DemandVersionService demandVersionService;

    @Resource
    private MassProductionHandoverService productionHandoverService;

    @Override
    public List<DeliveryPlanVO2> selectVO2ByPlanPeriod(String scenario, String planPeriod, String startTime,
                                                       String endTime) {
        return deliveryPlanService.selectVO2ByPlanPeriod(planPeriod, startTime, endTime);
    }

    @Override
    public List<DeliveryPlanVO2> selectVO2ByDeliveryVersionId(String scenario, String deliveryVersionId) {
        return deliveryPlanService.selectVO2ByDeliveryVersionId(deliveryVersionId);
    }

    @Override
    public List<InventoryDataDTO> selectInventoryByProductCodes(String scenario, List<String> productCodes) {
        return inventoryRealTimeDataService.selectInventoryByProductCodes(productCodes);
    }

    @Override
    public List<DeliveryPlanVO2> selectConsistenceDemandForecastDataByPlanPeriod(String scenario, String planPeriod,
                                                                                 String startTime, String endTime) {
        return consistenceDemandForecastDataService.selectVO2ByPlanPeriod(planPeriod, startTime, endTime);
    }

    @Override
    public List<ConsistenceDemandForecastVersionVO> selectConsistenceDemandForecastVersion(String scenario) {
        return consistenceDemandForecastVersionDao.selectAllVersionVO();
    }

    @Override
    public List<PartRiskLevelVO> selectMaterialRiskLeveByProductCodeList(String scenario,
                                                                         List<String> productCodeList) {
        return partRiskLevelService.selectByProductCodeList(productCodeList);
    }

    @Override
    public List<SafetyStockLevelVO> selectSafetyStockLevelByProductCodeList(String scenario,
                                                                            List<String> productCodeList) {
        return safetyStockLevelService.selectByProductCodeList(productCodeList);
    }

    @Override
    public List<LoadingDemandSubmissionVO> selectLoadingDemandSubmissionByProductCode(String scenario,
                                                                                      List<String> productCodeList) {
        return loadingDemandSubmissionService.selectByProductCode(productCodeList);
    }

    @Override
    public List<DeliveryPlanVO> queryAll(String scenario) {
        return deliveryPlanService.selectAll();
    }

    @Override
    public List<DeliveryPlanVO> queryCopy(String scenario) {
        return deliveryPlanService.queryCopy();
    }

    @Override
    public BaseResponse<Void> handleSaleOrganizes(String scenario, List<ErpSaleOrganize> o) {
        if (CollectionUtils.isEmpty(o)) {
            return BaseResponse.success();
        }
        List<SaleOrganizeVO> saleOrganizeVOS = saleOrganizeService.selectAll();
        Map<String, SaleOrganizeVO> oldSaleOrganizeMap =
                saleOrganizeVOS.stream().collect(Collectors.toMap(SaleOrganizeVO::getId, Function.identity(), (v1,
                                                                                                               v2) -> v1));
        List<SaleOrganizeDTO> insertSaleOrganizeDTOs = Lists.newArrayList();
        List<SaleOrganizeDTO> updateSaleOrganizeDTOs = Lists.newArrayList();
        for (ErpSaleOrganize erpSaleOrganize : o) {
            SaleOrganizeDTO saleOrganizeDTO = new SaleOrganizeDTO();
            String ebsOrgId = String.valueOf(erpSaleOrganize.getEbsOrgId());
            if (oldSaleOrganizeMap.containsKey(ebsOrgId)) {
                // 更新销售组织
                saleOrganizeDTO.setId(ebsOrgId);
                saleOrganizeDTO.setSaleOrgCode(erpSaleOrganize.getOrgCode());
                saleOrganizeDTO.setSaleOrgName(erpSaleOrganize.getOrgName());
                saleOrganizeDTO.setEnabled(oldSaleOrganizeMap.get(ebsOrgId).getEnabled());
                saleOrganizeDTO.setVersionValue(oldSaleOrganizeMap.get(ebsOrgId).getVersionValue());
                updateSaleOrganizeDTOs.add(saleOrganizeDTO);
            } else {
                // 新增销售组织
                saleOrganizeDTO.setId(ebsOrgId);
                saleOrganizeDTO.setSaleOrgCode(erpSaleOrganize.getOrgCode());
                saleOrganizeDTO.setSaleOrgName(erpSaleOrganize.getOrgName());
                saleOrganizeDTO.setEnabled(YesOrNoEnum.YES.getCode());
                insertSaleOrganizeDTOs.add(saleOrganizeDTO);
            }
        }
        if (CollectionUtils.isNotEmpty(insertSaleOrganizeDTOs)) {
            saleOrganizeService.doCreateBatch(insertSaleOrganizeDTOs);
        }
        if (CollectionUtils.isNotEmpty(updateSaleOrganizeDTOs)) {
            saleOrganizeService.doUpdateBatch(updateSaleOrganizeDTOs);
        }
        return BaseResponse.success();
    }

    @Override
    public BaseResponse<Void> handleCustomers(String scenario, List<ErpCustomer> o) {
        if (CollectionUtils.isEmpty(o)) {
            return BaseResponse.success();
        }
        List<OemVO> oemList = oemService.selectAll();
        Map<String, OemVO> oldOemMap = oemList.stream().collect(Collectors.toMap(OemVO::getId,
                Function.identity(), (v1, v2) -> v1));
        List<OemDTO> insertList = Lists.newArrayList();
        List<OemDTO> updateList = Lists.newArrayList();
        OemConvertor instance = OemConvertor.INSTANCE;
        for (ErpCustomer erpCustomer : o) {
            if (erpCustomer.getEbsOuId() != 324 && erpCustomer.getEbsOuId() != 1724) {
                // 主机厂名称为空不解析 2024/10/06 业务顾问确认
                continue;
            }
            OemDTO oemDTO = new OemDTO();
            String oemCode = String.join("_", erpCustomer.getCustomerNumber(),
                    String.valueOf(erpCustomer.getSiteNumber()));
            String oemId = String.join("_", String.valueOf(erpCustomer.getEbsCustomerId()),
                    String.valueOf(erpCustomer.getEbsSiteId()), String.valueOf(erpCustomer.getShipToSiteUseId()));
            String enabled = "A".equals(erpCustomer.getSiteStatus()) ? YesOrNoEnum.YES.getCode() :
                    YesOrNoEnum.NO.getCode();
            String ediFlag = "Y".equals(erpCustomer.getEdiFlag()) ? YesOrNoEnum.YES.getCode() :
                    YesOrNoEnum.NO.getCode();
            if (oldOemMap.containsKey(oemId)) {
                // 更新销售组织
                OemVO oldOemVO = oldOemMap.get(oemId);
                oemDTO = instance.vo2Dto(oldOemVO);
                oemDTO.setSaleOrgId(String.valueOf(erpCustomer.getEbsOuId()));
                oemDTO.setCustomerCode(erpCustomer.getCustomerNumber());
                oemDTO.setCustomerName(erpCustomer.getCustomerName());
                oemDTO.setLocationCode(String.valueOf(erpCustomer.getSiteNumber()));
                oemDTO.setLocationArea1(erpCustomer.getAddress1());
                oemDTO.setLocationArea2(erpCustomer.getAddress2());
                oemDTO.setLocationArea3(erpCustomer.getAddress3());
                oemDTO.setLocationArea4(erpCustomer.getAddress4());
                oemDTO.setPaymentTerm(erpCustomer.getPaymentTerms());
                oemDTO.setEdiLocation(erpCustomer.getEdiLocation());
                oemDTO.setPlantCode(erpCustomer.getAttribute5());
                oemDTO.setEbsCustomerId(String.valueOf(erpCustomer.getEbsCustomerId()));
                oemDTO.setEbsSiteId(String.valueOf(erpCustomer.getEbsSiteId()));
                oemDTO.setShipToSiteUseId(String.valueOf(erpCustomer.getShipToSiteUseId()));
                oemDTO.setSiteCountry(erpCustomer.getSiteCountry());
                oemDTO.setLastUpdateTime(erpCustomer.getLastUpdateDate());
                oemDTO.setEdiFlag(ediFlag);
                oemDTO.setOemName(erpCustomer.getAttribute1());
                oemDTO.setEnabled(enabled);
                updateList.add(oemDTO);
            } else {
                // 新增销售组织
                oemDTO.setId(oemId);
                oemDTO.setOemCode(oemCode);
                oemDTO.setSaleOrgId(String.valueOf(erpCustomer.getEbsOuId()));
                oemDTO.setCustomerCode(erpCustomer.getCustomerNumber());
                oemDTO.setCustomerName(erpCustomer.getCustomerName());
                oemDTO.setLocationCode(String.valueOf(erpCustomer.getSiteNumber()));
                oemDTO.setLocationArea1(erpCustomer.getAddress1());
                oemDTO.setLocationArea2(erpCustomer.getAddress2());
                oemDTO.setLocationArea3(erpCustomer.getAddress3());
                oemDTO.setLocationArea4(erpCustomer.getAddress4());
                oemDTO.setPaymentTerm(erpCustomer.getPaymentTerms());
                oemDTO.setEdiLocation(erpCustomer.getEdiLocation());
                oemDTO.setPlantCode(erpCustomer.getAttribute5());
                oemDTO.setEbsCustomerId(String.valueOf(erpCustomer.getEbsCustomerId()));
                oemDTO.setEbsSiteId(String.valueOf(erpCustomer.getEbsSiteId()));
                oemDTO.setShipToSiteUseId(String.valueOf(erpCustomer.getShipToSiteUseId()));
                oemDTO.setSiteCountry(erpCustomer.getSiteCountry());
                oemDTO.setLastUpdateTime(erpCustomer.getLastUpdateDate());
                oemDTO.setEdiFlag(ediFlag);
                oemDTO.setTransitClause("");
                oemDTO.setBusinessType("");
                oemDTO.setMarketType("");
                oemDTO.setOemName(erpCustomer.getAttribute1());
                oemDTO.setEnabled(enabled);
                insertList.add(oemDTO);
            }
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            oemService.doCreateBatch(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            oemService.doUpdateBatch(updateList);
        }
        return BaseResponse.success();
    }

    @Override
    public List<ConsistenceDemandForecastDataVO> selectDemandForecastDataByParams(String scenario, Map<String,
            Object> params) {
        return consistenceDemandForecastDataService.selectByParams(params);
    }

    @Override
    public List<ConsistenceDemandForecastDataDetailVO> selectDemandForecastByStockPointId(String scenario, String id) {
        return consistenceDemandForecastDataDetailService.selectDemandForecastBystockPointId(id);
    }

    @Override
    public List<ConsistenceDemandForecastDataDetailVO> selectDemandForecastByDataDetailByParams(String scenario,
                                                                                                Map<String, Object> params) {
        return consistenceDemandForecastDataDetailService.selectByParams(params);
    }

    @Override
    public List<ConsistenceDemandForecastDataDetailVO> selectDemandForecastByDataDetailVOByParams(
            String scenario, Map<String, Object> params) {
        return consistenceDemandForecastDataDetailService.selectVOByParams(params);
    }

    @Override
    public List<ConsistenceDemandForecastDataDetailVO> selectDemandForecastByDataDetailForecastQuantitySumByOemCodesAndMonths(
            String scenario, Map<String, Object> params) {
        return consistenceDemandForecastDataDetailService.selectForecastQuantitySumByOemCodesAndMonths(params);
    }

    @Override
    public List<InventoryRealTimeDataVO> selectInventoryRealTimeDataByParams(String scenario,
                                                                             Map<String, Object> params) {
        return inventoryRealTimeDataService.selectByParams(params);
    }

    @Override
    public List<DeliveryPlanVO2> selectConsistenceDemandForecastDataByVersionId(String scenario,
                                                                                List<String> versionIds) {
        return consistenceDemandForecastDataService.selectVO2ByVersionId(versionIds);
    }

    @Override
    public List<WorkHourStatisticsVO> dfpResourceCalendarCalWorkHour(String scenario, ResourceCalendarRangeDTO dto) {
        List<String> list = Arrays.asList(dto.getStandardResourceId().split(","));
        return dfpResourceCalendarService.calWorkHour(
                list,
                dto.getPhysicalResourceIds(),
                dto.getTimeUnit(),
                dto.getStartDate(),
                dto.getEndDate(),
                null);
    }

    @Override
    public List<OemVO> selectOemByParams(String scenario, Map<String, Object> params) {
        return oemService.selectByParams(params);
    }

    /**
     * 手动同步库存批次明细数据
     *
     * @param scenario
     * @param mesInventoryBatchDetails
     * @return
     */
    @Override
    public BaseResponse<Void> handleInventoryBatchDetail(String scenario,
                                                         List<MesInventoryBatchDetail> mesInventoryBatchDetails,List<String> orgIds) {
       return inventoryBatchDetailService.syncMes(scenario,mesInventoryBatchDetails,orgIds);
    }

    @Override
    public BaseResponse<Void> updatePartMappingData(String scenario, List<PartRelationMapVO> list) {
        return partRelationMapService.updatePartRelationMappingData(list);
    }

    @Override
    public BaseResponse<Void> updateMDMPartMappingData(String scenario, List<PartRelationMapVO> list) {
        return partRelationMapService.updateMDMPartMappingData(list);
    }

    @Override
    public List<InventoryBatchDetailVO> selectInventoryDataByProductCodes(String dfpScenario,
                                                                          List<String> productCodes,
                                                                          String stockPointType) {
        return inventoryBatchDetailService.selectByProductCodes(productCodes, stockPointType);
    }


    @Override
    public BaseResponse<Void> updateSyncNewProductTrialSubmissionData(String scenario,
                                                                      List<NewProductTrialSubmissionDTO> list) {
        return newProductTrialSubmissionService.updateNewProductTrialSubmissionData(list);
    }

    @Override
    public List<InventoryBatchDetailVO> selectInventoryDataByParams(String dfpScenario, Map<String, Object> params) {
        return inventoryBatchDetailService.selectByParamMap(params);
    }

    @Override
    public List<InventoryBatchDetailVO> selectInventoryBatchDetailByParams(String dfpScenario, Map<String, Object> params) {
        return inventoryBatchDetailService.selectVOByParams(params);
    }

    @Override
    public List<OemStockPointMapVO> selectOemStockPointByOemCodes(List<String> oemCodes) {
        return oemStockPointMapService.selectOemStockPointByOemCodes(oemCodes);
    }

    @Override
    public List<WarehouseReleaseRecordVO> selectWarehouseReleaseRecordByParams(String scenario,
                                                                               Map<String, Object> params) {
        return warehouseReleaseRecordService.selectByParams(params);
    }

    @Override
    public DeliveryPlanVersionVO selectNewestDeliveryPlanVersion(String scenario) {
        return deliveryPlanVersionService.selectNewestDeliveryPlanVersion();
    }

    @Override
    public List<CleanDemandDataDetailVO> selectCleanDemandDataDetailVOByVersionId(List<String> versionIds) {
        return cleanDemandDataDetailService.selectByParams(ImmutableMap.of("versionIds", versionIds));
    }

    @Override
    public List<DeliveryPlanPublishedVO> getDeliveryPlanFuture30Days(String scenario, List<String> planUserFactoryCodeList) {
        List<Date> dateList = new ArrayList<>();
        Date date = new Date();
        Date moveDay = DateUtils.moveDay(date, 30);
        log.info("查询未来30天的数据:{}", JSON.toJSONString(dateList));
        return deliveryPlanPublishedService.selectByParams(ImmutableMap.of(
                "startTimeStr", DateUtils.dateToString(date),
                "endTimeStr", DateUtils.dateToString(moveDay),
                "productCodes", planUserFactoryCodeList));
    }

    @Override
    public BaseResponse<Void> syncWareHouseReleaseData(String scenario, List<WarehouseReleaseRecordDTO> list) {
        //通过租户获取对应的本厂编码
        List<Scenario> scenarios = ipsNewFeign.selectByScenario(scenario);
        if (scenarios.isEmpty()) {
            log.error("没找到对应的scenario数据");
            throw new BusinessException("没找到对应的scenario数据");
        }
        String alternativeColumn = scenarios.get(0).getAlternativeColumn();
        if (StringUtils.isEmpty(alternativeColumn)) {
            log.error("auth_scenario表中对应的数据没有公司组织id这个字段");
            throw new BusinessException("auth_scenario表中对应的数据没有公司组织id这个字段");
        }
        HashMap<String, Object> map1 = MapUtil.newHashMap(1);
        map1.put("organizeId", alternativeColumn);
        List<NewStockPointVO> newStockPointVOS = newMdsFeign.selectStockPointByParams(scenario, map1);
        if (newStockPointVOS.isEmpty()) {
            log.error("没找到对应的库存点数据");
            throw new BusinessException("没找到对应的库存点数据");
        }
        return warehouseReleaseRecordService.syncWareHouseReleaseData(list, newStockPointVOS.get(0).getStockPointCode());
    }

    @Override
    public BaseResponse<Void> syncWarehouseLog(String scenario, List<WarehouseReleaseRecordLogDTO> list) {
        return warehouseReleaseRecordLogService.syncWarehouseLog(list);
    }

    @Override
    public BaseResponse<Void> handleTransportRouting(String scenario, List<MesTransportRouting> o) {
        return transportRoutingService.updateTransportRouting(o);
    }
	@Override
	public List<LabelValue<String>> selectAllVehicleModelCodes(String scenario) {
		List<OemVehicleModelVO> allList = OemVehicleModelService.selectAll();
		List<String> oemVehicleModelCodes = allList.stream().map(OemVehicleModelVO::getOemVehicleModelCode).distinct().collect(Collectors.toList());
		return oemVehicleModelCodes.stream()
                .map(x -> new LabelValue<>(x, x))
                .collect(Collectors.toList());
	}

    @Override
    public BaseResponse<Void> handleOemAddress(String scenario, List<OemAddressInventoryLogDTO> o) {
        return oemAddressInventoryLogService.syncOemAddressLog(o);
    }

    @Override
    public BaseResponse<Void> syncOriginDemandLog(String scenario, List<FdpOriginDemandInterfaceLogDTO> list, String importType) {
        return originDemandInterfaceLogService.syncOriginDemandLog(list,importType);
    }

    @Override
    public BaseResponse<Void> syncOriginDemandForecastLog(String scenario, List<FdpOriginDemandForecastInterfaceLogDTO> list, String importType) {
        return originDemandForecastInterfaceLogService.syncOriginDemandForecastLog(list,importType);
    }

    @Override
    public BaseResponse<Void> synTransportRouting(String scenario, String tenantId) {
        return transportRoutingInterfaceLogService.syncTransportRoutings(tenantId);
    }

    @Override
    public BaseResponse<Void> synCustomer(String scenario, String organizeId, String tenantId) {
        return oemService.syncCustomers(organizeId,tenantId);
    }

    @Override
    public List<OemStockPointMapVO> selectOemStockPointByStockPointCode(String scenario, List<String> stockPointByStockList) {
        return oemStockPointMapService.selectOemStockPointByStockPointCode(stockPointByStockList);
    }

    @Override
    public List<DeliveryPlanVO2> selectDeliveryPlanPublishedByParams(String scenario, Map<String, Object> params) {
        List<DeliveryPlanVO2> result = new ArrayList<>();
        List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOList = deliveryPlanPublishedService.selectByParams(params);
        deliveryPlanPublishedVOList.forEach(t -> {
            DeliveryPlanVO2 deliveryPlanVO2 = new DeliveryPlanVO2();
            deliveryPlanVO2.setDeliveryPlanId(t.getDeliveryPlanDataId());
            deliveryPlanVO2.setVersionId(t.getDeliveryVersionId());
            deliveryPlanVO2.setOemCode(t.getOemCode());
            deliveryPlanVO2.setProductCode(t.getProductCode());
            deliveryPlanVO2.setDemandQuantity(t.getDemandQuantity());
            deliveryPlanVO2.setDemandTime(t.getDemandTime());
            deliveryPlanVO2.setCustomerForecastsQuantity(t.getDemandQuantity());
            result.add(deliveryPlanVO2);
        });
        return result;
    }

    @Override
    public List<InventoryBatchDetailVO> selectInventoryBatchDetailVOByParams(String scenario, Map<String, Object> params) {
        return inventoryBatchDetailService.selectByParamMap(params);
    }


    @Override
    public List<InventoryBatchDetailVO> selectAllGlassInventoryBatch(String scenario) {
        return inventoryBatchDetailService.selectAllGlassInventoryBatch();
    }

    @Override
    public List<OemVehicleModelVO> selectOemVehicleModelByVehicleModelCode(String scenario, List<String> vehicleModelCodeList) {
        return OemVehicleModelService.selectOemByVehicleCode(vehicleModelCodeList);
    }

    @Override
    public List<CleanDemandDataVO> selectCleanDemandDataByVersionId(List<String> versionIdList) {
        return cleanDemandDataService.selectByParams(ImmutableMap.of("versionIds", versionIdList));
    }

    @Override
    public List<CleanDemandDataDetailVO> selectCleanDemandDataDetailVOByDataIds(List<String> cleanDemandDataIdList) {
        return cleanDemandDataDetailService.selectByParams(ImmutableMap.of("cleanDemandDataIds", cleanDemandDataIdList));
    }

    @Override
    public BaseResponse<Void> syncWareHouseReleaseFYSLData(String scenario, List<WarehouseReleaseRecordDTO> arrayList) {
        return warehouseReleaseRecordService.syncWareHouseReleaseFYSLData(arrayList);
    }

    @Override
    public List<DeliveryPlanPublishedVO> selectAllPublishDeliveryPlan() {
        return deliveryPlanPublishedService.selectAll();
    }

    @Override
    public List<DeliveryPlanPublishedVO> selectPublishDeliveryPlanByParams(Map<String, Object> params) {
        return deliveryPlanPublishedService.selectByParams(params);
    }

    @Override
    public List<PassengerCarSaleVO> selectPassengerCarSaleByParams(String scenario, Map<String, Object> params) {
        return passengerCarSaleService.selectByParams(params);
    }


    @Override
    public List<OriginalFilmInTransitVO> selectOriginalFilmInTransitByParams(Map<String, Object> params) {
        return originalFilmInTransitService.selectByParams(params);
    }

    @Override
    public BaseResponse<Void> handleErpInventoryBatchDetail(String scenario, List<ErpInventoryBatchDetail> erpInventoryBatchDetails, String orgId) {
        return inventoryBatchDetailService.syncErp(scenario, erpInventoryBatchDetails, orgId);
    }

    @Override
    public BaseResponse<Void> syncConsignmentProductData(String scenario, List<ConsignmentProductDTO> list) {
        return consignmentProductService.syncConsignmentProductData(list);
    }

    @Override
    public BaseResponse<Void> dockingOrderFeedback(String scenario, DeliveryDockingOrderApiDTO deliveryDockingOrderApiDTO) {
        try{
            return deliveryDockingOrderService.doDockingOrderFeedback(deliveryDockingOrderApiDTO);
        }catch (Exception e){
            return BaseResponse.error(e.getLocalizedMessage());
        }
    }

    @Override
    public String selectNewdemandVersion(String scenario) {
        return originDemandVersionService.selectLatestVersionId();
    }

    @Override
    public List<CleanDemandDataDetailVO> selectCleanDemandDetail(String scenario, List<String> cleanDemandIdList, String scopeStart, String scopeEnd) {
        return cleanDemandDataDetailService.selectCleanDemandDetail(cleanDemandIdList, scopeStart, scopeEnd);
    }

    @Override
    public PageInfo<InventoryBatchDetailVO> selectInventoryBatchDetailPage(Map<String, Object> params) {
        return inventoryBatchDetailService.selectByParamsPage(params);
    }

    @Override
    public PageInfo<InventoryBatchDetailVO> selectInventoryBatchDetailPageCustomize(Pagination pagination, String sortParam, String queryCriteriaParam, String overdueSort) {
        return inventoryBatchDetailService.selectInventoryBatchDetailPageCustomize(pagination,sortParam,queryCriteriaParam,overdueSort);
    }

    @Override
    public void addInventoryBatchDetail(List<InventoryBatchDetailDTO> inventoryBatchDetailDTOList) {
        inventoryBatchDetailService.doCreateBatch(inventoryBatchDetailDTOList);
    }

    @Override
    public void updateInventoryBatchDetail(List<InventoryBatchDetailDTO> inventoryBatchDetailDTOList) {
        inventoryBatchDetailService.doUpdateBatch(inventoryBatchDetailDTOList);
    }

    @Override
    public void deleteInventoryBatchDetail(List<String> ids) {
        inventoryBatchDetailService.doDelete(ids);
    }

    @Override
    public List<InventoryBatchDetailVO> selectInventoryBatchDetailVOByParams02(Map<String, Object> params) {
        return inventoryBatchDetailService.selectVOByParams(params);
    }

    @Override
    public List<InventoryBatchDetailVO> selectInventoryBatchDetailVOByParams03(String scenario, Map<String, Object> params) {
        return inventoryBatchDetailService.selectVOByParams(params);
    }

    @Override
    public BaseResponse<Void> syncCarrierData(String scenario, List<CarrierDataDTO> list) {
        return carrierDataService.syncCarrierData(list);
    }

    @Override
    public BaseResponse<Void> updateDeliveryDockingStatus(String scenario, List<GrpEdiDeliveryDetail> list) {
        return deliveryDockingOrderService.updateDeliveryDockingStatus(list);
    }

    @Override
    public BaseResponse<Void> updateMesDeliveryDockingStatus(String data, Map<String, Object> map) {
        return deliveryDockingOrderService.updateMesDeliveryDockingStatus(map);
    }

    @Override
    public BaseResponse<Void> syncFYDSData(String scenario, List<EnRouteDTO> list) {
        return warehouseReleaseRecordService.syncFYDSData(list);
    }

	@Override
	public List<InventoryBatchDetailVO> selectCollectByGroupType(String dfpScenario, String groupTyep) {
		return inventoryBatchDetailService.selectCollectByGroupType(groupTyep);
	}

    @Override
    public BaseResponse<Void> deliveryPlanPublishedCompareDoEnable(String dfpScenario, Map<String, Object> params) {
        deliveryPlanPublishedCompareService.doEnable(params);
        return BaseResponse.success();
    }

	@Override
	public List<DeliveryPlanPublishedVO> selectSumDeliveryPlanPublished(Map<String, Object> params) {
		return deliveryPlanPublishedService.selectSumDeliveryPlanPublished(params);
	}

    @Override
    public List<InventoryShiftVO> selectInventoryShiftList(Map<String, Object> map) {
        return inventoryShiftService.selectVOByParams(map);
    }

    @Override
    public List<CleanDemandDataVO> selectCleanDemandDataByParams(Map<String, Object> params) {
        return cleanDemandDataService.selectByParams(params);
    }

    @Override
    public String selectConsistenceDemandLatestVersionId(String scenario) {
        return consistenceDemandForecastVersionDao.selectLatestVersionId();
    }

    @Override
    public List<WarehouseReleaseRecordVO> selectWarehouseReleaseRecordByDate(Date startDate, Date endDate) {
        return warehouseReleaseRecordService.actualDelivery(startDate, endDate, null);
    }

    @Override
    public List<WarehouseReleaseRecordVO> selectWarehouseReleaseRecordSumQtyByDate(String scenario, List<String> productCodes, String startDate, String endDate) {
        return warehouseReleaseRecordService.selectWarehouseReleaseRecordSumQtyByDate(productCodes, startDate, endDate);
    }

    @Override
    public List<OemVO> selectOemVOByParams(String scenario, Map<String, Object> params) {
        return oemService.selectVOByParams(params);
    }

    @Override
    public List<DeliveryPlanPublishedVO> selectDeliveryPlanPublishedByParamOnDynamicColumns(String scenario, FeignDynamicParam feignDynamicParam) {
        return deliveryPlanPublishedService.selectDeliveryPlanPublishedByParamOnDynamicColumns(feignDynamicParam.getDynamicColumnParam(), feignDynamicParam.getQueryParam());
    }

    @Override
    public List<NewProductTrialSubmissionDetailVO> selectNewProductTrialSubmissionDetailByParams(String scenario, Map<String, Object> params) {
        return newProductTrialSubmissionDetailService.selectByParams(params);
    }

    @Override
    public List<NewProductTrialSubmissionDetailVO> selectSubmissionDetailByPrimaryKeys(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return NewProductTrialSubmissionDetailConvertor.INSTANCE.po2Vos(newProductTrialSubmissionDetailDao.selectByPrimaryKeys(ids));
    }

    @Override
    public ConsistenceDemandForecastVersionVO selectConsistenceDemandForecastVersionLatestPublished(String scenario) {
        return consistenceDemandForecastVersionService.selectConsistenceDemandForecastVersionLatestPublished();
    }

    @Override
    public DeliveryPlanVersionVO selectLatestDeliveryPlanVersion(String scenario) {
        return deliveryPlanVersionService.selectLatestVersionByParams(new HashMap<>());
    }

	@Override
	public List<PartRelationMapVO> selectPartRelationMapByParams(String scenario, Map<String, Object> params) {
		return partRelationMapService.selectByParams(params);
	}

    @Override
    public List<OemRiskLevelVO> selectOemRiskLevelByParams(String scenario, Map<String, Object> params) {
        return oemRiskLevelService.selectByParams(params);
    }

    @Override
    public void syncPartNumFromErp(Map<String, Object> params) {
        partRelationMapService.syncPartNumFromErp(params);
    }

    @Override
    public List<CleanForecastDataVO> selectCleanForecastDataVOListByParams(String scenario, Map<String, Object> params) {
        return cleanForecastDataService.selectByParams(params);
    }

    @Override
    public List<CleanForecastDataDetailVO> selectCleanForecastDataDetailVOListByParams(String scenario, Map<String, Object> params) {
        return cleanForecastDataDetailService.selectByParams(params);
    }

    @Override
    public List<DemandVersionVO> selectDemandVersionVOListByParams(String scenario, Map<String, Object> params) {
        return demandVersionService.selectByParams(params);
    }

    @Override
    public BaseResponse<Void> syncCreateProductionHandover(String scenario, String ids, String oaId) {
        return productionHandoverService.syncCreateProductionHandover(ids, oaId);
    }

    @Override
    public BaseResponse<Void> syncProductionHandoverStatus(String scenario, List<MassProductionHandoverDTO> dtoS) {
        return productionHandoverService.syncProductionHandoverStatus(dtoS);
    }
}



