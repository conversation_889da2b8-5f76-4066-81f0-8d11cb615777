package com.yhl.scp.dfp.consistence.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.consistence.convertor.ConsistenceDemandForecastDataDetailConvertor;
import com.yhl.scp.dfp.consistence.domain.entity.ConsistenceDemandForecastDataDetailDO;
import com.yhl.scp.dfp.consistence.domain.service.ConsistenceDemandForecastDataDetailDomainService;
import com.yhl.scp.dfp.consistence.dto.ConsistenceDemandForecastCheckDTO;
import com.yhl.scp.dfp.consistence.dto.ConsistenceDemandForecastDataDetailDTO;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastDataDetailDao;
import com.yhl.scp.dfp.consistence.infrastructure.po.ConsistenceDemandForecastDataDetailPO;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataDetailService;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>ConsistenceDemandForecastDataDetailServiceImpl</code>
 * <p>
 * 一致性业务预测数据明细应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-01 19:56:39
 */
@Slf4j
@Service
public class ConsistenceDemandForecastDataDetailServiceImpl extends AbstractService
        implements ConsistenceDemandForecastDataDetailService {

    @Resource
    private ConsistenceDemandForecastDataDetailDao consistenceDemandForecastDataDetailDao;

    @Resource
    private ConsistenceDemandForecastDataDetailDomainService consistenceDemandForecastDataDetailDomainService;

    @Override
    public BaseResponse<Void> doCreate(ConsistenceDemandForecastDataDetailDTO consistenceDemandForecastDataDetailDTO) {
        // 0.数据转换
        ConsistenceDemandForecastDataDetailDO consistenceDemandForecastDataDetailDO =
                ConsistenceDemandForecastDataDetailConvertor.INSTANCE.dto2Do(consistenceDemandForecastDataDetailDTO);
        ConsistenceDemandForecastDataDetailPO consistenceDemandForecastDataDetailPO =
                ConsistenceDemandForecastDataDetailConvertor.INSTANCE.dto2Po(consistenceDemandForecastDataDetailDTO);
        // 1.数据校验
        consistenceDemandForecastDataDetailDomainService.validation(consistenceDemandForecastDataDetailDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(consistenceDemandForecastDataDetailPO);
        consistenceDemandForecastDataDetailDao.insert(consistenceDemandForecastDataDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(ConsistenceDemandForecastDataDetailDTO consistenceDemandForecastDataDetailDTO) {
        // 0.数据转换
        ConsistenceDemandForecastDataDetailDO consistenceDemandForecastDataDetailDO =
                ConsistenceDemandForecastDataDetailConvertor.INSTANCE.dto2Do(consistenceDemandForecastDataDetailDTO);
        ConsistenceDemandForecastDataDetailPO consistenceDemandForecastDataDetailPO =
                ConsistenceDemandForecastDataDetailConvertor.INSTANCE.dto2Po(consistenceDemandForecastDataDetailDTO);
        // 1.数据校验
        consistenceDemandForecastDataDetailDomainService.validation(consistenceDemandForecastDataDetailDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(consistenceDemandForecastDataDetailPO);
        consistenceDemandForecastDataDetailDao.update(consistenceDemandForecastDataDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ConsistenceDemandForecastDataDetailDTO> list) {
        List<ConsistenceDemandForecastDataDetailPO> newList =
                ConsistenceDemandForecastDataDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        consistenceDemandForecastDataDetailDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<ConsistenceDemandForecastDataDetailDTO> list) {
        List<ConsistenceDemandForecastDataDetailPO> newList =
                ConsistenceDemandForecastDataDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        consistenceDemandForecastDataDetailDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return consistenceDemandForecastDataDetailDao.deleteBatch(idList);
        }
        return consistenceDemandForecastDataDetailDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ConsistenceDemandForecastDataDetailVO selectByPrimaryKey(String id) {
        ConsistenceDemandForecastDataDetailPO po = consistenceDemandForecastDataDetailDao.selectByPrimaryKey(id);
        return ConsistenceDemandForecastDataDetailConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "CONSISTENCE_DEMAND_FORECAST_DATA_DETAIL")
    public List<ConsistenceDemandForecastDataDetailVO> selectByPage(Pagination pagination, String sortParam,
                                                                    String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "CONSISTENCE_DEMAND_FORECAST_DATA_DETAIL")
    public List<ConsistenceDemandForecastDataDetailVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ConsistenceDemandForecastDataDetailVO> dataList =
                consistenceDemandForecastDataDetailDao.selectByCondition(sortParam, queryCriteriaParam);
        ConsistenceDemandForecastDataDetailServiceImpl target =
                SpringBeanUtils.getBean(ConsistenceDemandForecastDataDetailServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ConsistenceDemandForecastDataDetailVO> selectByParams(Map<String, Object> params) {
        List<ConsistenceDemandForecastDataDetailPO> list = consistenceDemandForecastDataDetailDao.selectByParams(params);
        return ConsistenceDemandForecastDataDetailConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ConsistenceDemandForecastDataDetailVO> selectVOByParams(Map<String, Object> params) {
        return consistenceDemandForecastDataDetailDao.selectVOByParams(params);
    }

    @Override
    public List<ConsistenceDemandForecastDataDetailVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.CONSISTENCE_DEMAND_FORECAST_DATA_DETAIL.getCode();
    }

    @Override
    public List<ConsistenceDemandForecastDataDetailVO> invocation(List<ConsistenceDemandForecastDataDetailVO> dataList,
                                                                  Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isNotEmpty(versionDTOList)) {
            return consistenceDemandForecastDataDetailDao.deleteBatchVersion(versionDTOList);
        }
        return 0;
    }

    @Override
    public List<ConsistenceDemandForecastDataDetailVO> selectDemandForecastBystockPointId(String id) {
        return consistenceDemandForecastDataDetailDao.selectDemandForecastByStockPointId(id);
    }

    @Override
    public List<ConsistenceDemandForecastDataDetailVO> selectByConsistenceDemandForecastDataId() {
        return Collections.emptyList();
    }


    @Override
    public List<ConsistenceDemandForecastDataDetailVO> getLatestPublishedVersionData(List<String> oemCodeScope,
                                                                                     List<String> productScope) {
        // 查询最新已发布的预测版本号
        String versionCode = consistenceDemandForecastDataDetailDao.selectLatestPublishedVersionCode();
        if (StringUtils.isEmpty(versionCode)) {
            return new ArrayList<>();
        }
        // 业务预测数据
        return consistenceDemandForecastDataDetailDao.selectVOByParams(ImmutableMap
                .of("versionCode", versionCode, "oemCodeList", oemCodeScope, "productCodeList", productScope));
    }

    @Override
    public List<ConsistenceDemandForecastDataDetailVO> selectForecastQuantitySumByOemCodesAndMonths(Map<String, Object> params) {
        return consistenceDemandForecastDataDetailDao.selectForecastQuantitySumByOemCodesAndMonths(params);
    }

    @Override
    public List<ConsistenceDemandForecastDataDetailVO> selectByParentIdsAndMonth(List<String> ids, Date month) {
        List<ConsistenceDemandForecastDataDetailPO> poList =
                consistenceDemandForecastDataDetailDao.selectByParentIdsAndMonth(ids, month);
        if (CollectionUtils.isNotEmpty(poList)) {
            return ConsistenceDemandForecastDataDetailConvertor.INSTANCE.po2Vos(poList);
        }
        return Collections.emptyList();
    }

	@Override
	public void doBatchUapdatForecastQuantity(List<ConsistenceDemandForecastCheckDTO> dtoList) {
		List<String> ids = dtoList.stream().map(ConsistenceDemandForecastCheckDTO::getId).collect(Collectors.toList());
		List<ConsistenceDemandForecastDataDetailPO> currList = consistenceDemandForecastDataDetailDao.selectByPrimaryKeys(ids);
		Map<String,ConsistenceDemandForecastCheckDTO> CheckDTOMap = dtoList.stream().collect(Collectors.toMap(ConsistenceDemandForecastCheckDTO::getId,e->e,(v1, v2) -> v1));
		for (ConsistenceDemandForecastDataDetailPO curr : currList) {
			ConsistenceDemandForecastCheckDTO checkDTO = CheckDTOMap.get(curr.getId());
			curr.setForecastQuantity(checkDTO.getDeliveryQty().add(checkDTO.getWaitDeliveryQty()));
		}
		BasePOUtils.updateBatchFiller(currList);
		consistenceDemandForecastDataDetailDao.updateBatch(currList);
	}
}
