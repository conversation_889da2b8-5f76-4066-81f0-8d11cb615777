package com.yhl.scp.dfp.massProduction.domain.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.yhl.platform.common.ddd.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <code>MassProductionHandoverDetailDO</code>
 * <p>
 * 量产移交信息详情表DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 16:20:22
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MassProductionHandoverDetailDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -55976444473455306L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 主表id
     */
    private String massProductionHandoverId;
    /**
     * 本厂编码
     */
    private String productCode;
    /**
     * 物料名称
     */
    private String productName;
    /**
     * 零件号
     */
    private String partNumber;
    /**
     * 装车位置小类
     */
    private String loadingPositionSub;
    /**
     * 包装方式
     */
    private String boxType;
    /**
     * 单片玻璃重量
     */
    private BigDecimal pieceWeight;
    /**
     * 料箱立项重量
     */
    private BigDecimal boxWeight;
    /**
     * 包装箱规格(成箱片数)
     */
    private Integer boxSpec;
    /**
     * 长
     */
    private BigDecimal productLength;
    /**
     * 宽
     */
    private BigDecimal productWidth;
    /**
     * 厚
     */
    private BigDecimal productThickness;
    /**
     * SOP
     */
    private Date productSop;
    /**
     * EOP
     */
    private Date productEop;
    /**
     * 零件履历
     */
    private String partRecord;
    /**
     * 版本
     */
    private Integer versionValue;
    
    /**
     * 是否修改人员权限
     */
    private String updateOrderPlannerFlag;
    
    /**
     * 开发类型
     */
    private String produceType;

}
