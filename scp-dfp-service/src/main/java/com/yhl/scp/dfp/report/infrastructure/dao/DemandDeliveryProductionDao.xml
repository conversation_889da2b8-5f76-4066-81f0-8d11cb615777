<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.report.infrastructure.dao.DemandDeliveryProductionDao">
    
    <resultMap id="DemandDeliveryProductionResultMap" type="com.yhl.scp.dfp.report.vo.DemandDeliveryProductionVO">
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="demand_category" jdbcType="VARCHAR" property="demandCategory"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="boh_stock" jdbcType="DECIMAL" property="bohStock"/>
        <result column="transporting_qty" jdbcType="DECIMAL" property="transportingQty"/>
        <result column="fg_stock" jdbcType="DECIMAL" property="fgStock"/>
        <result column="after_packing" jdbcType="DECIMAL" property="afterPacking"/>
        <result column="after_lamination" jdbcType="DECIMAL" property="afterLamination"/>
        <result column="after_shape" jdbcType="DECIMAL" property="afterShape"/>
        <result column="schedule_qty" jdbcType="DECIMAL" property="scheduleQty"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
    </resultMap>

    <select id="selectByPageWithParams" resultMap="DemandDeliveryProductionResultMap">
        SELECT DISTINCT
            dp.product_code,
            psp.vehicle_model_code,
            dp.demand_category,
            dp.oem_code,
            0 as boh_stock,
            0 as transporting_qty,
            0 as fg_stock,
            0 as after_packing,
            0 as after_lamination,
            0 as after_shape,
            0 as schedule_qty,
            'MAIN' as category
        FROM v_fdp_delivery_plan_published dp
        LEFT JOIN (
            SELECT DISTINCT product_code, vehicle_model_code
            FROM v_mds_product_stock_point
            WHERE enabled = 'YES'
        ) psp ON dp.product_code = psp.product_code
        <where>
            dp.enabled = 'YES'
            AND dp.demand_time >= CURDATE()
            AND dp.demand_time &lt;= DATE_ADD(CURDATE(), INTERVAL 14 DAY)
            <if test="params.productCode != null and params.productCode != ''">
                AND dp.product_code LIKE CONCAT('%', #{params.productCode}, '%')
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                AND psp.vehicle_model_code = #{params.vehicleModelCode}
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                AND dp.oem_code = #{params.oemCode}
            </if>
            <if test="params.demandCategory != null and params.demandCategory != ''">
                AND dp.demand_category = #{params.demandCategory}
            </if>
        </where>
        ORDER BY dp.product_code
    </select>
    
    <!-- 查询总数 -->
    <select id="countByParams" resultType="long">
        SELECT COUNT(DISTINCT dp.product_code)
        FROM v_fdp_delivery_plan_published dp
        LEFT JOIN (
            SELECT DISTINCT product_code, vehicle_model_code
            FROM v_mds_product_stock_point
            WHERE enabled = 'YES'
        ) psp ON dp.product_code = psp.product_code
        <where>
            dp.enabled = 'YES'
            AND dp.demand_time >= CURDATE()
            AND dp.demand_time &lt;= DATE_ADD(CURDATE(), INTERVAL 14 DAY)
            <if test="params.productCode != null and params.productCode != ''">
                AND dp.product_code LIKE CONCAT('%', #{params.productCode}, '%')
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                AND psp.vehicle_model_code = #{params.vehicleModelCode}
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                AND dp.oem_code = #{params.oemCode}
            </if>
            <if test="params.demandCategory != null and params.demandCategory != ''">
                AND dp.demand_category = #{params.demandCategory}
            </if>
        </where>
    </select>
    
    <select id="selectByCondition" resultMap="DemandDeliveryProductionResultMap">
        SELECT demand_category, oem_code, vehicle_model_code
        FROM v_fdp_delivery_plan_published
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            ORDER BY ${sortParam}
        </if>
    </select>
    
</mapper>
