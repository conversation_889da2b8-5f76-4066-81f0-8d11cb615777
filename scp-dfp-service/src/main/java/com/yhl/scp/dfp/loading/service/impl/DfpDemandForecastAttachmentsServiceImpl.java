package com.yhl.scp.dfp.loading.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.common.MinioUtils;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.loading.convertor.DfpDemandForecastAttachmentsConvertor;
import com.yhl.scp.dfp.loading.domain.entity.DfpDemandForecastAttachmentsDO;
import com.yhl.scp.dfp.loading.domain.service.DfpDemandForecastAttachmentsDomainService;
import com.yhl.scp.dfp.loading.dto.DfpDemandForecastAttachmentsDTO;
import com.yhl.scp.dfp.loading.enums.FileTypeEnum;
import com.yhl.scp.dfp.loading.infrastructure.dao.DfpDemandForecastAttachmentsDao;
import com.yhl.scp.dfp.loading.infrastructure.po.DfpDemandForecastAttachmentsPO;
import com.yhl.scp.dfp.loading.service.DfpDemandForecastAttachmentsService;
import com.yhl.scp.dfp.loading.vo.DfpDemandForecastAttachmentsVO;
import io.minio.MinioClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>DfpDemandForecastAttachmentsServiceImpl</code>
 * <p>
 * 源文件管理应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-03 14:38:29
 */
@Slf4j
@Service
public class DfpDemandForecastAttachmentsServiceImpl extends AbstractService
        implements DfpDemandForecastAttachmentsService {

    @Resource
    private DfpDemandForecastAttachmentsDao dfpDemandForecastAttachmentsDao;

    @Resource
    private DfpDemandForecastAttachmentsDomainService dfpDemandForecastAttachmentsDomainService;

    @Value("${minio.bucket}")
    private String bucketName;

    @Override
    public BaseResponse<Void> doCreate(DfpDemandForecastAttachmentsDTO dfpDemandForecastAttachmentsDTO) {
        // 0.数据转换
        DfpDemandForecastAttachmentsDO dfpDemandForecastAttachmentsDO =
                DfpDemandForecastAttachmentsConvertor.INSTANCE.dto2Do(dfpDemandForecastAttachmentsDTO);
        DfpDemandForecastAttachmentsPO dfpDemandForecastAttachmentsPO =
                DfpDemandForecastAttachmentsConvertor.INSTANCE.dto2Po(dfpDemandForecastAttachmentsDTO);
        // 1.数据校验
        dfpDemandForecastAttachmentsDomainService.validation(dfpDemandForecastAttachmentsDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(dfpDemandForecastAttachmentsPO);
        dfpDemandForecastAttachmentsDao.insertWithPrimaryKey(dfpDemandForecastAttachmentsPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(DfpDemandForecastAttachmentsDTO dfpDemandForecastAttachmentsDTO) {
        // 0.数据转换
        DfpDemandForecastAttachmentsDO dfpDemandForecastAttachmentsDO =
                DfpDemandForecastAttachmentsConvertor.INSTANCE.dto2Do(dfpDemandForecastAttachmentsDTO);
        DfpDemandForecastAttachmentsPO dfpDemandForecastAttachmentsPO =
                DfpDemandForecastAttachmentsConvertor.INSTANCE.dto2Po(dfpDemandForecastAttachmentsDTO);
        // 1.数据校验
        dfpDemandForecastAttachmentsDomainService.validation(dfpDemandForecastAttachmentsDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(dfpDemandForecastAttachmentsPO);
        dfpDemandForecastAttachmentsDao.update(dfpDemandForecastAttachmentsPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<DfpDemandForecastAttachmentsDTO> list) {
        List<DfpDemandForecastAttachmentsPO> newList = DfpDemandForecastAttachmentsConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        dfpDemandForecastAttachmentsDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<DfpDemandForecastAttachmentsDTO> list) {
        List<DfpDemandForecastAttachmentsPO> newList = DfpDemandForecastAttachmentsConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        dfpDemandForecastAttachmentsDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return dfpDemandForecastAttachmentsDao.deleteBatch(idList);
        }
        return dfpDemandForecastAttachmentsDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public DfpDemandForecastAttachmentsVO selectByPrimaryKey(String id) {
        DfpDemandForecastAttachmentsPO po = dfpDemandForecastAttachmentsDao.selectByPrimaryKey(id);
        return DfpDemandForecastAttachmentsConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_dfp_demand_forecast_attachments")
    public List<DfpDemandForecastAttachmentsVO> selectByPage(Pagination pagination, String sortParam,
                                                             String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_dfp_demand_forecast_attachments")
    public List<DfpDemandForecastAttachmentsVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<DfpDemandForecastAttachmentsVO> dataList =
                dfpDemandForecastAttachmentsDao.selectByCondition(sortParam, queryCriteriaParam);
        DfpDemandForecastAttachmentsServiceImpl target =
                SpringBeanUtils.getBean(DfpDemandForecastAttachmentsServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<DfpDemandForecastAttachmentsVO> selectByParams(Map<String, Object> params) {
        List<DfpDemandForecastAttachmentsPO> list = dfpDemandForecastAttachmentsDao.selectByParams(params);
        return DfpDemandForecastAttachmentsConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<DfpDemandForecastAttachmentsVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.DFP_DEMAND_FORECAST_ATTACHMENTS.getCode();
    }

    @Override
    public List<DfpDemandForecastAttachmentsVO> invocation(List<DfpDemandForecastAttachmentsVO> dataList,
                                                           Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public void upload(String versionCode, String uploadStatus, Map<String,List<MultipartFile>> fileMap){
        MinioClient minioClient = SpringBeanUtils.getBean(MinioClient.class);

        MinioUtils minioUtils = new MinioUtils(minioClient);
        for (Map.Entry<String, List<MultipartFile>> entry : fileMap.entrySet()) {
            for (MultipartFile multipartFile : entry.getValue()) {
                this.upload(versionCode,entry.getKey(), uploadStatus, multipartFile,minioUtils);
            }
        }
    }

    @Override
    public void uploadFile(String versionCode, String oemCode, String uploadStatus, MultipartFile file) {
        MinioClient minioClient = SpringBeanUtils.getBean(MinioClient.class);

        MinioUtils minioUtils = new MinioUtils(minioClient);
        this.upload(versionCode,oemCode, uploadStatus, file,minioUtils);
    }

    public void upload(String versionCode, String oemCode, String uploadStatus, MultipartFile file,MinioUtils minioUtils){


        String fileName = minioUtils.upload(bucketName,file);

        DfpDemandForecastAttachmentsDTO dto = new DfpDemandForecastAttachmentsDTO();
        dto.setVersionCode(versionCode);
        dto.setFileType(fileName);
        dto.setOemCode(oemCode);
        dto.setFileName(fileName);
        dto.setUploadStatus(uploadStatus);
        dto.setFileType(FileTypeEnum.SOURCE_FILE.getCode());
        if(StringUtils.isEmpty(oemCode)) {
        	dto.setFileType(FileTypeEnum.IMPORT_FILE.getCode());
        }
        this.doCreate(dto);
    }

    @Override
    public void download(String id, HttpServletResponse res) {
        MinioClient minioClient = SpringBeanUtils.getBean(MinioClient.class);
        MinioUtils minioUtils = new MinioUtils(minioClient);

        DfpDemandForecastAttachmentsVO vo = this.selectByPrimaryKey(id);
        if (vo == null) {
            throw new RuntimeException("该数据不存在！");
        }
        minioUtils.download(bucketName, vo.getFileName(), res);

    }

    @Override
    public void remove(String id) {
        MinioClient minioClient = SpringBeanUtils.getBean(MinioClient.class);
        MinioUtils minioUtils = new MinioUtils(minioClient);

        DfpDemandForecastAttachmentsVO vo = this.selectByPrimaryKey(id);
        minioUtils.remove(bucketName, vo.getFileName());
        List<String> list = new ArrayList<>();
        list.add(id);
        this.doDelete(list);
    }

    @Override
    public List<String> listObjects() {
        MinioClient minioClient = SpringBeanUtils.getBean(MinioClient.class);
        MinioUtils minioUtils = new MinioUtils(minioClient);
        List<String> list = new ArrayList<>();
        minioUtils.listObjects(bucketName).forEach(item -> list.add(item.objectName()));
        return list;
    }

}