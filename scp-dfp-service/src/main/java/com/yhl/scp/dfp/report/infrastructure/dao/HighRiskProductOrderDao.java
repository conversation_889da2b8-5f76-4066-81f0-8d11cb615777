package com.yhl.scp.dfp.report.infrastructure.dao;

import com.yhl.scp.dfp.report.vo.HighRiskProductOrderVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <code>HighRiskProductOrderDao</code>
 * <p>
 * HighRiskProductOrderDao
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-14 23:22:32
 */
public interface HighRiskProductOrderDao {

    List<HighRiskProductOrderVO> selectByVersionId(@Param("versionId") String versionId);

    List<HighRiskProductOrderVO> selectByCondition(@Param("sortParam") String sortParam,
                                               @Param("queryCriteriaParam") String queryCriteriaParam);

    List<HighRiskProductOrderVO> selectStatistics(@Param("sortParam") String sortParam,
                                                  @Param("queryCriteriaParam") String queryCriteriaParam);

}
