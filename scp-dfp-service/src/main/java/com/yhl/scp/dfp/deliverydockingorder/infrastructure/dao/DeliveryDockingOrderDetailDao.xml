<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.deliverydockingorder.infrastructure.dao.DeliveryDockingOrderDetailDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.deliverydockingorder.infrastructure.po.DeliveryDockingOrderDetailPO">
        <!--@Table fdp_delivery_docking_order_detail-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="delivery_docking_number" jdbcType="VARCHAR" property="deliveryDockingNumber"/>
        <result column="delivery_docking_line_number" jdbcType="VARCHAR" property="deliveryDockingLineNumber"/>
        <result column="delivery_docking_id" jdbcType="VARCHAR" property="deliveryDockingId"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="delivery_quantity" jdbcType="INTEGER" property="deliveryQuantity"/>
        <result column="must_quantity" jdbcType="INTEGER" property="mustQuantity"/>
        <result column="material_equipment" jdbcType="VARCHAR" property="materialEquipment"/>
        <result column="box_number" jdbcType="VARCHAR" property="boxNumber"/>
        <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime"/>
        <result column="gross_weight" jdbcType="VARCHAR" property="grossWeight"/>
        <result column="volume" jdbcType="VARCHAR" property="volume"/>
        <result column="cabinet_type" jdbcType="VARCHAR" property="cabinetType"/>
        <result column="bill_lading_number" jdbcType="VARCHAR" property="billLadingNumber"/>
        <result column="invoice_number" jdbcType="VARCHAR" property="invoiceNumber"/>
        <result column="actual_delivery_quantity" jdbcType="INTEGER" property="actualDeliveryQuantity"/>
        <result column="actual_delivery_box_quantity" jdbcType="INTEGER" property="actualDeliveryBoxQuantity"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="version_code" jdbcType="VARCHAR" property="versionCode"/>
        <result column="demand_sources_id" jdbcType="VARCHAR" property="demandSourcesId"/>
        <result column="data_sources" jdbcType="VARCHAR" property="dataSources"/>
        <result column="business_type" jdbcType="VARCHAR" property="businessType"/>
        <result column="market_type" jdbcType="VARCHAR" property="marketType"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="written_off_flag" jdbcType="VARCHAR" property="writtenOffFlag"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.deliverydockingorder.vo.DeliveryDockingOrderDetailVO">
        <!-- TODO -->
<!--        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>-->
<!--        <result column="customer_code" jdbcType="VARCHAR" property="customerCode"/>-->
<!--        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>-->
<!--        <result column="transport_mode" jdbcType="VARCHAR" property="transportMode"/>-->
    </resultMap>
    <sql id="Base_Column_List">
        id,
		delivery_docking_number,
		delivery_docking_line_number,
		delivery_docking_id,
		product_code,
		oem_code,
		delivery_quantity,
		must_quantity,
		material_equipment,
		box_number,
		delivery_time,
		gross_weight,
		volume,
		cabinet_type,
		bill_lading_number,
		invoice_number,
		actual_delivery_quantity,
		actual_delivery_box_quantity,
		STATUS,
		version_code,
		demand_sources_id,
		data_sources,
		business_type,
		market_type,
		remark,
		enabled,
		creator,
		create_time,
		modifier,
		modify_time,
		version_value,
		written_off_flag
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.deliveryDockingNumber != null and params.deliveryDockingNumber != ''">
                and delivery_docking_number = #{params.deliveryDockingNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.deliveryDockingNumbers != null and params.deliveryDockingNumbers != ''">
                and delivery_docking_number in
                <foreach collection="params.deliveryDockingNumbers" item="item" index="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.deliveryDockingLineNumber != null and params.deliveryDockingLineNumber != ''">
                and delivery_docking_line_number = #{params.deliveryDockingLineNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.deliveryDockingId != null and params.deliveryDockingId != ''">
                and delivery_docking_id = #{params.deliveryDockingId,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.deliveryQuantity != null">
                and delivery_quantity = #{params.deliveryQuantity,jdbcType=INTEGER}
            </if>
            <if test="params.mustQuantity != null">
                and must_quantity = #{params.mustQuantity,jdbcType=INTEGER}
            </if>
            <if test="params.materialEquipment != null and params.materialEquipment != ''">
                and material_equipment = #{params.materialEquipment,jdbcType=VARCHAR}
            </if>
            <if test="params.boxNumber != null and params.boxNumber != ''">
                and box_number = #{params.boxNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.deliveryTime != null">
                and delivery_time = #{params.deliveryTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.grossWeight != null">
                and gross_weight = #{params.grossWeight,jdbcType=VARCHAR}
            </if>
            <if test="params.volume != null">
                and volume = #{params.volume,jdbcType=VARCHAR}
            </if>
            <if test="params.cabinetType != null and params.cabinetType != ''">
                and cabinet_type = #{params.cabinetType,jdbcType=VARCHAR}
            </if>
            <if test="params.billLadingNumber != null and params.billLadingNumber != ''">
                and bill_lading_number = #{params.billLadingNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.invoiceNumber != null and params.invoiceNumber != ''">
                and invoice_number = #{params.invoiceNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.actualDeliveryQuantity != null">
                and actual_delivery_quantity = #{params.actualDeliveryQuantity,jdbcType=INTEGER}
            </if>
            <if test="params.actualDeliveryBoxQuantity != null">
                and actual_delivery_box_quantity = #{params.actualDeliveryBoxQuantity,jdbcType=INTEGER}
            </if>
            <if test="params.status != null and params.status != ''">
                and status = #{params.status,jdbcType=VARCHAR}
            </if>
            <if test="params.versionCode != null and params.versionCode != ''">
                and version_code = #{params.versionCode,jdbcType=VARCHAR}
            </if>
            <if test="params.demandSourcesId != null and params.demandSourcesId != ''">
                and demand_sources_id = #{params.demandSourcesId,jdbcType=VARCHAR}
            </if>
            <if test="params.dataSources != null and params.dataSources != ''">
                and data_sources = #{params.dataSources,jdbcType=VARCHAR}
            </if>
            <if test="params.businessType != null and params.businessType != ''">
                and business_type = #{params.businessType,jdbcType=VARCHAR}
            </if>
            <if test="params.marketType != null and params.marketType != ''">
                and market_type = #{params.marketType,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.writtenOffFlag != null and params.writtenOffFlag != ''">
                and written_off_flag = #{params.writtenOffFlag,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_delivery_docking_order_detail
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_delivery_docking_order_detail
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_fdp_delivery_docking_order_detail
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_delivery_docking_order_detail
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List" />
        from v_fdp_delivery_docking_order_detail
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.deliverydockingorder.infrastructure.po.DeliveryDockingOrderDetailPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_delivery_docking_order_detail(
        id,
        delivery_docking_number,
        delivery_docking_line_number,
        delivery_docking_id,
        product_code,
        oem_code,
        delivery_quantity,
        must_quantity,
        material_equipment,
        box_number,
        delivery_time,
        gross_weight,
        volume,
        cabinet_type,
        bill_lading_number,
        invoice_number,
        actual_delivery_quantity,
        actual_delivery_box_quantity,
        status,
        version_code,
        demand_sources_id,
        data_sources,
        business_type,
        market_type,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
		written_off_flag)
        values (
        #{id,jdbcType=VARCHAR},
        #{deliveryDockingNumber,jdbcType=VARCHAR},
        #{deliveryDockingLineNumber,jdbcType=VARCHAR},
        #{deliveryDockingId,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{oemCode,jdbcType=VARCHAR},
        #{deliveryQuantity,jdbcType=INTEGER},
        #{mustQuantity,jdbcType=INTEGER},
        #{materialEquipment,jdbcType=VARCHAR},
        #{boxNumber,jdbcType=VARCHAR},
        #{deliveryTime,jdbcType=TIMESTAMP},
        #{grossWeight,jdbcType=VARCHAR},
        #{volume,jdbcType=VARCHAR},
        #{cabinetType,jdbcType=VARCHAR},
        #{billLadingNumber,jdbcType=VARCHAR},
        #{invoiceNumber,jdbcType=VARCHAR},
        #{actualDeliveryQuantity,jdbcType=INTEGER},
        #{actualDeliveryBoxQuantity,jdbcType=INTEGER},
        #{status,jdbcType=VARCHAR},
        #{versionCode,jdbcType=VARCHAR},
        #{demandSourcesId,jdbcType=VARCHAR},
        #{dataSources,jdbcType=VARCHAR},
        #{businessType,jdbcType=VARCHAR},
        #{marketType,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{writtenOffFlag,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.deliverydockingorder.infrastructure.po.DeliveryDockingOrderDetailPO">
        insert into fdp_delivery_docking_order_detail(
        id,
        delivery_docking_number,
        delivery_docking_line_number,
        delivery_docking_id,
        product_code,
        oem_code,
        delivery_quantity,
        must_quantity,
        material_equipment,
        box_number,
        delivery_time,
        gross_weight,
        volume,
        cabinet_type,
        bill_lading_number,
        invoice_number,
        actual_delivery_quantity,
        actual_delivery_box_quantity,
        status,
        version_code,
        demand_sources_id,
        data_sources,
        business_type,
        market_type,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
		written_off_flag)
        values (
        #{id,jdbcType=VARCHAR},
        #{deliveryDockingNumber,jdbcType=VARCHAR},
        #{deliveryDockingLineNumber,jdbcType=VARCHAR},
        #{deliveryDockingId,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{oemCode,jdbcType=VARCHAR},
        #{deliveryQuantity,jdbcType=INTEGER},
        #{mustQuantity,jdbcType=INTEGER},
        #{materialEquipment,jdbcType=VARCHAR},
        #{boxNumber,jdbcType=VARCHAR},
        #{deliveryTime,jdbcType=TIMESTAMP},
        #{grossWeight,jdbcType=VARCHAR},
        #{volume,jdbcType=VARCHAR},
        #{cabinetType,jdbcType=VARCHAR},
        #{billLadingNumber,jdbcType=VARCHAR},
        #{invoiceNumber,jdbcType=VARCHAR},
        #{actualDeliveryQuantity,jdbcType=INTEGER},
        #{actualDeliveryBoxQuantity,jdbcType=INTEGER},
        #{status,jdbcType=VARCHAR},
        #{versionCode,jdbcType=VARCHAR},
        #{demandSourcesId,jdbcType=VARCHAR},
        #{dataSources,jdbcType=VARCHAR},
        #{businessType,jdbcType=VARCHAR},
        #{marketType,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{writtenOffFlag,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_delivery_docking_order_detail(
        id,
        delivery_docking_number,
        delivery_docking_line_number,
        delivery_docking_id,
        product_code,
        oem_code,
        delivery_quantity,
        must_quantity,
        material_equipment,
        box_number,
        delivery_time,
        gross_weight,
        volume,
        cabinet_type,
        bill_lading_number,
        invoice_number,
        actual_delivery_quantity,
        actual_delivery_box_quantity,
        status,
        version_code,
        demand_sources_id,
        data_sources,
        business_type,
        market_type,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
		written_off_flag)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.deliveryDockingNumber,jdbcType=VARCHAR},
        #{entity.deliveryDockingLineNumber,jdbcType=VARCHAR},
        #{entity.deliveryDockingId,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR},
        #{entity.oemCode,jdbcType=VARCHAR},
        #{entity.deliveryQuantity,jdbcType=INTEGER},
        #{entity.mustQuantity,jdbcType=INTEGER},
        #{entity.materialEquipment,jdbcType=VARCHAR},
        #{entity.boxNumber,jdbcType=VARCHAR},
        #{entity.deliveryTime,jdbcType=TIMESTAMP},
        #{entity.grossWeight,jdbcType=VARCHAR},
        #{entity.volume,jdbcType=VARCHAR},
        #{entity.cabinetType,jdbcType=VARCHAR},
        #{entity.billLadingNumber,jdbcType=VARCHAR},
        #{entity.invoiceNumber,jdbcType=VARCHAR},
        #{entity.actualDeliveryQuantity,jdbcType=INTEGER},
        #{entity.actualDeliveryBoxQuantity,jdbcType=INTEGER},
        #{entity.status,jdbcType=VARCHAR},
        #{entity.versionCode,jdbcType=VARCHAR},
        #{entity.demandSourcesId,jdbcType=VARCHAR},
        #{entity.dataSources,jdbcType=VARCHAR},
        #{entity.businessType,jdbcType=VARCHAR},
        #{entity.marketType,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.writtenOffFlag,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_delivery_docking_order_detail(
        id,
        delivery_docking_number,
        delivery_docking_line_number,
        delivery_docking_id,
        product_code,
        oem_code,
        delivery_quantity,
        must_quantity,
        material_equipment,
        box_number,
        delivery_time,
        gross_weight,
        volume,
        cabinet_type,
        bill_lading_number,
        invoice_number,
        actual_delivery_quantity,
        actual_delivery_box_quantity,
        status,
        version_code,
        demand_sources_id,
        data_sources,
        business_type,
        market_type,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
		written_off_flag)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.deliveryDockingNumber,jdbcType=VARCHAR},
        #{entity.deliveryDockingLineNumber,jdbcType=VARCHAR},
        #{entity.deliveryDockingId,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR},
        #{entity.oemCode,jdbcType=VARCHAR},
        #{entity.deliveryQuantity,jdbcType=INTEGER},
        #{entity.mustQuantity,jdbcType=INTEGER},
        #{entity.materialEquipment,jdbcType=VARCHAR},
        #{entity.boxNumber,jdbcType=VARCHAR},
        #{entity.deliveryTime,jdbcType=TIMESTAMP},
        #{entity.grossWeight,jdbcType=VARCHAR},
        #{entity.volume,jdbcType=VARCHAR},
        #{entity.cabinetType,jdbcType=VARCHAR},
        #{entity.billLadingNumber,jdbcType=VARCHAR},
        #{entity.invoiceNumber,jdbcType=VARCHAR},
        #{entity.actualDeliveryQuantity,jdbcType=INTEGER},
        #{entity.actualDeliveryBoxQuantity,jdbcType=INTEGER},
        #{entity.status,jdbcType=VARCHAR},
        #{entity.versionCode,jdbcType=VARCHAR},
        #{entity.demandSourcesId,jdbcType=VARCHAR},
        #{entity.dataSources,jdbcType=VARCHAR},
        #{entity.businessType,jdbcType=VARCHAR},
        #{entity.marketType,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.writtenOffFlag,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.deliverydockingorder.infrastructure.po.DeliveryDockingOrderDetailPO">
        update fdp_delivery_docking_order_detail set
        delivery_docking_number = #{deliveryDockingNumber,jdbcType=VARCHAR},
        delivery_docking_line_number = #{deliveryDockingLineNumber,jdbcType=VARCHAR},
        delivery_docking_id = #{deliveryDockingId,jdbcType=VARCHAR},
        product_code = #{productCode,jdbcType=VARCHAR},
        oem_code = #{oemCode,jdbcType=VARCHAR},
        delivery_quantity = #{deliveryQuantity,jdbcType=INTEGER},
        must_quantity = #{mustQuantity,jdbcType=INTEGER},
        material_equipment = #{materialEquipment,jdbcType=VARCHAR},
        box_number = #{boxNumber,jdbcType=VARCHAR},
        delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
        gross_weight = #{grossWeight,jdbcType=VARCHAR},
        volume = #{volume,jdbcType=VARCHAR},
        cabinet_type = #{cabinetType,jdbcType=VARCHAR},
        bill_lading_number = #{billLadingNumber,jdbcType=VARCHAR},
        invoice_number = #{invoiceNumber,jdbcType=VARCHAR},
        actual_delivery_quantity = #{actualDeliveryQuantity,jdbcType=INTEGER},
        actual_delivery_box_quantity = #{actualDeliveryBoxQuantity,jdbcType=INTEGER},
        status = #{status,jdbcType=VARCHAR},
        version_code = #{versionCode,jdbcType=VARCHAR},
        demand_sources_id = #{demandSourcesId,jdbcType=VARCHAR},
        data_sources = #{dataSources,jdbcType=VARCHAR},
        business_type = #{businessType,jdbcType=VARCHAR},
        market_type = #{marketType,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER},
        written_off_flag = #{writtenOffFlag,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.deliverydockingorder.infrastructure.po.DeliveryDockingOrderDetailPO">
        update fdp_delivery_docking_order_detail
        <set>
            <if test="item.deliveryDockingNumber != null and item.deliveryDockingNumber != ''">
                delivery_docking_number = #{item.deliveryDockingNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.deliveryDockingLineNumber != null and item.deliveryDockingLineNumber != ''">
                delivery_docking_line_number = #{item.deliveryDockingLineNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.deliveryDockingId != null and item.deliveryDockingId != ''">
                delivery_docking_id = #{item.deliveryDockingId,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.deliveryQuantity != null">
                delivery_quantity = #{item.deliveryQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.mustQuantity != null">
                must_quantity = #{item.mustQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.materialEquipment != null and item.materialEquipment != ''">
                material_equipment = #{item.materialEquipment,jdbcType=VARCHAR},
            </if>
            <if test="item.boxNumber != null and item.boxNumber != ''">
                box_number = #{item.boxNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.deliveryTime != null">
                delivery_time = #{item.deliveryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.grossWeight != null">
                gross_weight = #{item.grossWeight,jdbcType=VARCHAR},
            </if>
            <if test="item.volume != null">
                volume = #{item.volume,jdbcType=VARCHAR},
            </if>
            <if test="item.cabinetType != null and item.cabinetType != ''">
                cabinet_type = #{item.cabinetType,jdbcType=VARCHAR},
            </if>
            <if test="item.billLadingNumber != null and item.billLadingNumber != ''">
                bill_lading_number = #{item.billLadingNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.invoiceNumber != null and item.invoiceNumber != ''">
                invoice_number = #{item.invoiceNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.actualDeliveryQuantity != null">
                actual_delivery_quantity = #{item.actualDeliveryQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.actualDeliveryBoxQuantity != null">
                actual_delivery_box_quantity = #{item.actualDeliveryBoxQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.status != null and item.status != ''">
                status = #{item.status,jdbcType=VARCHAR},
            </if>
            <if test="item.versionCode != null and item.versionCode != ''">
                version_code = #{item.versionCode,jdbcType=VARCHAR},
            </if>
            <if test="item.demandSourcesId != null and item.demandSourcesId != ''">
                demand_sources_id = #{item.demandSourcesId,jdbcType=VARCHAR},
            </if>
            <if test="item.dataSources != null and item.dataSources != ''">
                data_sources = #{item.dataSources,jdbcType=VARCHAR},
            </if>
            <if test="item.businessType != null and item.businessType != ''">
                business_type = #{item.businessType,jdbcType=VARCHAR},
            </if>
            <if test="item.marketType != null and item.marketType != ''">
                market_type = #{item.marketType,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.writtenOffFlag != null and item.writtenOffFlag != ''">
                written_off_flag = #{item.writtenOffFlag,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_delivery_docking_order_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="delivery_docking_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.deliveryDockingNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="delivery_docking_line_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.deliveryDockingLineNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="delivery_docking_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.deliveryDockingId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="oem_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="delivery_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.deliveryQuantity,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="must_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.mustQuantity,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="material_equipment = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialEquipment,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="box_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.boxNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="delivery_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.deliveryTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="gross_weight = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.grossWeight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="volume = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.volume,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cabinet_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.cabinetType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bill_lading_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.billLadingNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="invoice_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.invoiceNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="actual_delivery_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.actualDeliveryQuantity,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="actual_delivery_box_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.actualDeliveryBoxQuantity,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.status,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_sources_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandSourcesId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="data_sources = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.dataSources,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="business_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.businessType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="market_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.marketType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="written_off_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.writtenOffFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update fdp_delivery_docking_order_detail 
        <set>
            <if test="item.deliveryDockingNumber != null and item.deliveryDockingNumber != ''">
                delivery_docking_number = #{item.deliveryDockingNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.deliveryDockingLineNumber != null and item.deliveryDockingLineNumber != ''">
                delivery_docking_line_number = #{item.deliveryDockingLineNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.deliveryDockingId != null and item.deliveryDockingId != ''">
                delivery_docking_id = #{item.deliveryDockingId,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.deliveryQuantity != null">
                delivery_quantity = #{item.deliveryQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.mustQuantity != null">
                must_quantity = #{item.mustQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.materialEquipment != null and item.materialEquipment != ''">
                material_equipment = #{item.materialEquipment,jdbcType=VARCHAR},
            </if>
            <if test="item.boxNumber != null and item.boxNumber != ''">
                box_number = #{item.boxNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.deliveryTime != null">
                delivery_time = #{item.deliveryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.grossWeight != null">
                gross_weight = #{item.grossWeight,jdbcType=VARCHAR},
            </if>
            <if test="item.volume != null">
                volume = #{item.volume,jdbcType=VARCHAR},
            </if>
            <if test="item.cabinetType != null and item.cabinetType != ''">
                cabinet_type = #{item.cabinetType,jdbcType=VARCHAR},
            </if>
            <if test="item.billLadingNumber != null and item.billLadingNumber != ''">
                bill_lading_number = #{item.billLadingNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.invoiceNumber != null and item.invoiceNumber != ''">
                invoice_number = #{item.invoiceNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.actualDeliveryQuantity != null">
                actual_delivery_quantity = #{item.actualDeliveryQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.actualDeliveryBoxQuantity != null">
                actual_delivery_box_quantity = #{item.actualDeliveryBoxQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.status != null and item.status != ''">
                status = #{item.status,jdbcType=VARCHAR},
            </if>
            <if test="item.versionCode != null and item.versionCode != ''">
                version_code = #{item.versionCode,jdbcType=VARCHAR},
            </if>
            <if test="item.demandSourcesId != null and item.demandSourcesId != ''">
                demand_sources_id = #{item.demandSourcesId,jdbcType=VARCHAR},
            </if>
            <if test="item.dataSources != null and item.dataSources != ''">
                data_sources = #{item.dataSources,jdbcType=VARCHAR},
            </if>
            <if test="item.businessType != null and item.businessType != ''">
                business_type = #{item.businessType,jdbcType=VARCHAR},
            </if>
            <if test="item.marketType != null and item.marketType != ''">
                market_type = #{item.marketType,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.writtenOffFlag != null and item.writtenOffFlag != ''">
                written_off_flag = #{item.writtenOffFlag,jdbcType=VARCHAR},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from fdp_delivery_docking_order_detail where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_delivery_docking_order_detail where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <!-- 删除 -->
    <delete id="deleteByParams">
        delete from fdp_delivery_docking_order_detail
        <where>
            <if test="deliveryDockingNumber != null and deliveryDockingNumber != ''">
                and delivery_docking_number = #{deliveryDockingNumber}
            </if>
            <if test="deliveryDockingLineNumber != null and deliveryDockingLineNumber != ''">
                and delivery_docking_line_number = #{deliveryDockingLineNumber}
            </if>
            <if test="productCode != null and productCode != ''">
                and product_code = #{productCode}
            </if>
        </where>
    </delete>

</mapper>
