package com.yhl.scp.dfp.version.service.impl;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.dfp.common.constants.DfpConstants;
import com.yhl.scp.dfp.common.enums.GenerateTypeEnum;
import com.yhl.scp.dfp.common.enums.GranularityEnum;
import com.yhl.scp.dfp.common.enums.PublishStatusEnum;
import com.yhl.scp.dfp.common.enums.VersionTypeEnum;
import com.yhl.scp.dfp.delivery.convertor.DeliveryPlanConvertor;
import com.yhl.scp.dfp.delivery.convertor.DeliveryPlanDetailConvertor;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanDTO;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanDetailDTO;
import com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanVersionPO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanDetailVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.stock.convertor.InventoryShiftConvertor;
import com.yhl.scp.dfp.stock.dto.InventoryShiftDTO;
import com.yhl.scp.dfp.stock.vo.InventoryShiftVO;
import com.yhl.scp.dfp.verison.dto.OemResourceDTO;
import com.yhl.scp.dfp.verison.dto.VersionCreateDTO;
import com.yhl.scp.dfp.version.service.DemandVersionCreateBase;
import com.yhl.scp.ips.utils.BasePOUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>DeliveryPlanVersionCreate</code>
 * <p>
 * 发货计划版本创建
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 09:42:44
 */
@Slf4j
@Component
public class DeliveryPlanVersionCreate extends DemandVersionCreateBase {

    @Override
    protected String createFirstDemandVersion(VersionCreateDTO versionCreateDTO) {
        // 根据计划周期查询当前计划周期一级需求版本是否存在
        DeliveryPlanVersionPO firstVersion =
                deliveryPlanVersionDao.selectFirstVersionInfoByPlanPeriod(versionCreateDTO.getPlanPeriod());
        if (Objects.nonNull(firstVersion)) {
            return firstVersion.getId();
        }
        DeliveryPlanVersionPO newFirstPlanVersionPo = new DeliveryPlanVersionPO();
        newFirstPlanVersionPo.setPlanPeriod(versionCreateDTO.getPlanPeriod());
        newFirstPlanVersionPo.setGenerateType(versionCreateDTO.getGenerateType());
        BasePOUtils.insertFiller(newFirstPlanVersionPo, versionCreateDTO.getCreator());
        deliveryPlanVersionDao.insertWithPrimaryKey(newFirstPlanVersionPo);
        return newFirstPlanVersionPo.getId();
    }

    @Override
    protected String createSecondDemandVersion(VersionCreateDTO versionCreateDTO, String newVersionCode,
                                               String firstVersionId) {
        // 查询新生成的版本号是否存在
        DeliveryPlanVersionPO secondPlanVersionPo =
                deliveryPlanVersionDao.selectSecondVersionInfoByVersionCode(newVersionCode);
        if (Objects.nonNull(secondPlanVersionPo)) {
            return secondPlanVersionPo.getId();
        }
        // 二级需求生成
        DeliveryPlanVersionPO nextPlanVersionPo = new DeliveryPlanVersionPO();
        nextPlanVersionPo.setId(versionCreateDTO.getCurrentVersionId());
        nextPlanVersionPo.setPlanPeriod(versionCreateDTO.getPlanPeriod());
        nextPlanVersionPo.setPlanHorizon(30);
        nextPlanVersionPo.setPlanGranularity(GranularityEnum.DAY.getCode());
        nextPlanVersionPo.setDemandVersionId(versionCreateDTO.getTargetCleanDemandVersionId());
        nextPlanVersionPo.setVersionCode(newVersionCode);
        nextPlanVersionPo.setParentVersionId(firstVersionId);
        nextPlanVersionPo.setVersionStatus(PublishStatusEnum.UNPUBLISH.getCode());
        nextPlanVersionPo.setGenerateType(versionCreateDTO.getGenerateType());
        nextPlanVersionPo.setDemandVersionId(versionCreateDTO.getTargetCleanDemandVersionId());
        BasePOUtils.insertFiller(nextPlanVersionPo, versionCreateDTO.getCreator());
        deliveryPlanVersionDao.insertWithPrimaryKey(nextPlanVersionPo);
        return nextPlanVersionPo.getId();
    }

    @Override
    protected void createThirdDemandVersion(VersionCreateDTO versionCreateDTO, String newVersionCode,
                                            List<String> oemCodeList, String secondVersionId) {
        List<DeliveryPlanVersionPO> sonVersionPoList = Lists.newArrayList();
        String lastDayVersionId = demandVersionDao.selectLatestVersionId(VersionTypeEnum.CLEAN_DEMAND.getCode());

        Map<String, String> demandVersionMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(versionCreateDTO.getOemCodeResource())) {
            demandVersionMap = versionCreateDTO.getOemCodeResource().stream().filter(t -> StringUtils
                    .isNotEmpty(t.getOemCode())).collect(Collectors.toMap(OemResourceDTO::getOemCode,
                    OemResourceDTO::getCleanDemandVersionId, (o1, o2) -> o1));
        }
        for (String oemCode : oemCodeList) {
            DeliveryPlanVersionPO sonDemandVersionPo = new DeliveryPlanVersionPO();
            sonDemandVersionPo.setPlanPeriod(versionCreateDTO.getPlanPeriod());
            sonDemandVersionPo.setPlanHorizon(30);
            sonDemandVersionPo.setPlanGranularity(GranularityEnum.DAY.getCode());
            sonDemandVersionPo.setVersionCode(newVersionCode);
            sonDemandVersionPo.setParentVersionId(secondVersionId);
            sonDemandVersionPo.setOemCode(oemCode);
            // 判断日需求是否为空，如果为空则获取计划周期内生成时间最晚的日需求版本
            sonDemandVersionPo.setDemandVersionId(demandVersionMap.getOrDefault(oemCode, lastDayVersionId));
            sonDemandVersionPo.setGenerateType(versionCreateDTO.getGenerateType());
            BasePOUtils.insertFiller(sonDemandVersionPo);
            sonVersionPoList.add(sonDemandVersionPo);
        }
        deliveryPlanVersionDao.insertBatchWithPrimaryKey(sonVersionPoList);
    }

    @Override
    protected void copyDemandVersionData(String tenantId, String secondVersionId, List<String> oemCodeList,
                                         VersionCreateDTO versionCreateDTO) {
        // 判断是否有最新的发货计划版本
        String targetDeliveryPlanVersionId = versionCreateDTO.getTargetDeliveryPlanVersionId();
        if (StringUtils.isBlank(targetDeliveryPlanVersionId)) {
            return;
        }
        // 查询发货计划版本数据
        Map<String, Object> params = new HashMap<>();
        params.put("versionId", targetDeliveryPlanVersionId);
        List<DeliveryPlanVO> planList = deliveryPlanService.selectByParams(params);
        if (CollectionUtils.isEmpty(planList)) {
            return;
        }
        List<String> deliveryPlanIdList = planList.stream().map(DeliveryPlanVO::getId).collect(Collectors.toList());

        // 查询发货计划明细数据
        List<DeliveryPlanDetailVO> detailList = new ArrayList<>();
        List<List<String>> partition = Lists.partition(deliveryPlanIdList, 1000);
        for (List<String> list : partition) {
            List<DeliveryPlanDetailVO> detailSubList = deliveryPlanDetailService.selectByPlanDetailIdList(list);
            if (CollectionUtils.isNotEmpty(detailSubList)) {
                detailList.addAll(detailSubList);
            }
        }

        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }
        Map<String, List<DeliveryPlanDetailVO>> deliveryPlanDetailVOMapOfDataId = detailList.stream()
                .collect(Collectors.groupingBy(DeliveryPlanDetailVO::getDeliveryPlanDataId));

        // 查询库存推移表数据
        List<InventoryShiftVO> inventoryShifts = inventoryShiftService.selectByParams(params);
        if (CollectionUtils.isEmpty(inventoryShifts)) {
            return;
        }

        // 开始数据复制
        List<DeliveryPlanDTO> newPlanList = new ArrayList<>();
        List<DeliveryPlanDetailDTO> newDetailList = new ArrayList<>();
        List<InventoryShiftDTO> newShiftList = new ArrayList<>();
        for (DeliveryPlanVO deliveryPlanVO : planList) {
            DeliveryPlanDTO newDeliveryPlanDTO = DeliveryPlanConvertor.INSTANCE.vo2Dto(deliveryPlanVO);
            newDeliveryPlanDTO.setVersionId(secondVersionId);
            newDeliveryPlanDTO.setPublishStatus(PublishStatusEnum.UNPUBLISH.getCode());
            newDeliveryPlanDTO.setId(UUIDUtil.getUUID());
            newPlanList.add(newDeliveryPlanDTO);
            List<DeliveryPlanDetailVO> planDetailVOS = deliveryPlanDetailVOMapOfDataId.get(deliveryPlanVO.getId());
            if (CollectionUtils.isNotEmpty(planDetailVOS)) {
                for (DeliveryPlanDetailVO planDetailVO : planDetailVOS) {
                    DeliveryPlanDetailDTO newDeliveryPlanDetailDTO = DeliveryPlanDetailConvertor.INSTANCE.vo2Dto(planDetailVO);
                    newDeliveryPlanDetailDTO.setDeliveryPlanDataId(newDeliveryPlanDTO.getId());
                    newDeliveryPlanDetailDTO.setId(UUIDUtil.getUUID());
                    newDetailList.add(newDeliveryPlanDetailDTO);
                }
            }
        }
        for (InventoryShiftVO inventoryShiftVO : inventoryShifts) {
            InventoryShiftDTO inventoryShiftDTO = InventoryShiftConvertor.INSTANCE.vo2Dto(inventoryShiftVO);
            inventoryShiftDTO.setVersionId(secondVersionId);
            inventoryShiftDTO.setId(UUIDUtil.getUUID());
            newShiftList.add(inventoryShiftDTO);
        }
        // 数据持久化
        if (CollectionUtils.isNotEmpty(newPlanList)) {
            Lists.partition(newPlanList, 500)
                    .forEach(subList -> deliveryPlanService.doCreateBatch(subList));
        }
        if (CollectionUtils.isNotEmpty(newDetailList)) {
            Lists.partition(newDetailList, 500)
                    .forEach(subList -> deliveryPlanDetailService.doCreateBatch(subList));
        }
        if (CollectionUtils.isNotEmpty(newShiftList)) {
            Lists.partition(newShiftList, 500)
                    .forEach(subList -> inventoryShiftService.doCreateBatch(subList));
        }
    }

    @Override
    protected String getVersionCode(VersionCreateDTO versionCreateDTO, List<String> oemCodeList) {
        String prefix = DfpConstants.DELIVERY_PLAN_VERSION_CODE_PRE + versionCreateDTO.getPlanPeriod();
        if (GenerateTypeEnum.AUTO.getCode().equals(versionCreateDTO.getGenerateType())) {
            return prefix + DfpConstants.DELIVERY_PLAN_VERSION_CODE;
        } else {
            // 如果手动创建版本，需要根据当前用户权限获取主机厂编码，并判断编码的最大版本号
            String versionCode = deliveryPlanVersionDao.selectMaxVersionCodeByOemCodeList(versionCreateDTO.getPlanPeriod(),
                    oemCodeList);
            return prefix + getNewVersionCode(versionCode);
        }
    }

    @Override
    protected List<String> getManualOemCodeList(VersionCreateDTO versionCreateDTO) {
        // 如果目标原始需求版本为空，则根据用户权限查询所属主机厂编码列表
        List<OemVO> oemVOList = super.getOemCodeByUserPermission();
        return oemVOList.stream()
                .map(OemVO::getOemCode)
                .collect(Collectors.toList());
    }

    @Override
    protected List<String> getAutoOemCodeList(VersionCreateDTO versionCreateDTO) {
        return oemService.selectAll().stream()
                .map(OemVO::getOemCode)
                .collect(Collectors.toList());
    }

    @Override
    public BaseResponse<Void> doPublishVersion(String versionType, String versionId) {
        return BaseResponse.success();
    }

}