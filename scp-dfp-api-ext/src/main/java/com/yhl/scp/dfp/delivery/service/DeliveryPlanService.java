package com.yhl.scp.dfp.delivery.service;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanCalculateDTO;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanDTO;
import com.yhl.scp.dfp.delivery.dto.InventoryAndDeliveryDTO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishCheckVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.dfp.delivery.vo.InventoryAndDeliveryDataVO;

/**
 * <code>DeliveryPlanService</code>
 * <p>
 * 发货计划表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 13:51:11
 */
public interface DeliveryPlanService extends BaseService<DeliveryPlanDTO, DeliveryPlanVO> {

    /**
     * 查询所有
     *
     * @return list {@link DeliveryPlanVO}
     */
    List<DeliveryPlanVO> selectAll();

    /**
     * 批量删除
     * @param versionDTOList
     */
    int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList);

    /**
     * 按计划周期获取最新发货计划
     * @param planPeriod
     * @return
     */
    List<DeliveryPlanVO2> selectVO2ByPlanPeriod(String planPeriod, String startTime, String endTime);

    /**
     * 按版本id获取最新发货计划
     * @param deliveryVersionId
     * @return
     */
    List<DeliveryPlanVO2> selectVO2ByDeliveryVersionId(String deliveryVersionId);

    /**
     * 发货计划保存
     * @param deliveryPlans
     */
    BaseResponse<Void> doDeliveryPlanSave(List<DeliveryPlanDTO> deliveryPlans);

    /**
     * 发货计划计算
     * @param deliveryPlanCalculateDTO
     */
    BaseResponse<Void> doDeliveryPlanCalc(DeliveryPlanCalculateDTO deliveryPlanCalculateDTO,boolean saved);
    /**
     * 使用上版计划
     * @param oemCodes 主机厂编码，多个以逗号分割
     * @param versionCode 当前发货版本
     * @param prevVersionCode 上版发货版本
     */
    void doUsePreviousPlan(String oemCodes, String versionCode, String prevVersionCode);

    List<DeliveryPlanVO> queryCopy();

    List<LabelValue<String>> queryOemDropdownInfo(String versionCode);

    BaseResponse<List<String>> doPublish(List<String> ids);
    /**
     * 导出数据
     *
     * @param response  响应体
     * @param versionCode 版本ID
     */
    void exportData(HttpServletResponse response, String versionCode);

    PageInfo<InventoryAndDeliveryDataVO> selectInventoryAndDelivery(InventoryAndDeliveryDTO inventoryAndDeliveryDTO);

    DeliveryPlanPublishCheckVO doPublishCheck(DeliveryPlanDTO deliveryPlanDTO);

	/**
	 * 发货计划编制报表分页查询
	 * @param pagination
	 * @param sortParam
	 * @param queryCriteriaParam
	 * @return
	 */
	List<DeliveryPlanVO> pageDeliveryReport(Pagination pagination, String sortParam, String queryCriteriaParam);

}
