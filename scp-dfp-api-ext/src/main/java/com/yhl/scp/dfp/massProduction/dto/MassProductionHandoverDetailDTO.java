package com.yhl.scp.dfp.massProduction.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MassProductionHandoverDetailDTO</code>
 * <p>
 * 量产移交信息详情表DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 16:19:48
 */
@ApiModel(value = "量产移交信息详情表DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MassProductionHandoverDetailDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -40458538071762221L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id")
    private String massProductionHandoverId;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    private String productCode;
    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String productName;
    /**
     * 零件号
     */
    @ApiModelProperty(value = "零件号")
    private String partNumber;
    /**
     * 装车位置小类
     */
    @ApiModelProperty(value = "装车位置小类")
    private String loadingPositionSub;
    /**
     * 包装方式
     */
    @ApiModelProperty(value = "包装方式")
    private String boxType;
    /**
     * 单片玻璃重量
     */
    @ApiModelProperty(value = "单片玻璃重量")
    private BigDecimal pieceWeight;
    /**
     * 料箱立项重量
     */
    @ApiModelProperty(value = "料箱立项重量")
    private BigDecimal boxWeight;
    /**
     * 包装箱规格(成箱片数)
     */
    @ApiModelProperty(value = "包装箱规格(成箱片数)")
    private Integer boxSpec;
    /**
     * 长
     */
    @ApiModelProperty(value = "长")
    private BigDecimal productLength;
    /**
     * 宽
     */
    @ApiModelProperty(value = "宽")
    private BigDecimal productWidth;
    /**
     * 厚
     */
    @ApiModelProperty(value = "厚")
    private BigDecimal productThickness;
    /**
     * SOP
     */
    @ApiModelProperty(value = "SOP")
    private Date productSop;
    /**
     * EOP
     */
    @ApiModelProperty(value = "EOP")
    private Date productEop;
    /**
     * 零件履历
     */
    @ApiModelProperty(value = "零件履历")
    private String partRecord;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer versionValue;
    
    /**
     * 是否修改人员权限
     */
    @ApiModelProperty(value = "是否修改人员权限")
    private String updateOrderPlannerFlag;
    
    /**
     * 开发类型
     */
    @ApiModelProperty(value = "开发类型")
    private String produceType;

}
