package com.yhl.scp.dfp.deliverydockingorder.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yhl.platform.common.ddd.BaseDTO;
import com.yhl.scp.common.excel.ExcelPropertyCheck;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>DeliveryDockingOrderDetailBasicDTO</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-29 21:57:24
 */
@ApiModel(value = "发货对接单详情基础DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryDockingOrderDetailBasicDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -24216016694630161L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 发货对接单号
     */
    @ApiModelProperty(value = "发货对接单号")
    private String deliveryDockingNumber;
    /**
     * 发货对接单行号
     */
    @ApiModelProperty(value = "发货对接单行号")
    private String deliveryDockingLineNumber;
    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id")
    private String deliveryDockingId;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @ExcelProperty(value = "本厂编码*")
    @ExcelPropertyCheck(required = true)
    private String productCode;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    private String oemCode;
    /**
     * 发货数量
     */
    @ApiModelProperty(value = "发货数量")
    @ExcelProperty(value = "发货数量*")
    @ExcelPropertyCheck(required = true)
    private Integer deliveryQuantity;
    /**
     * 必发数量
     */
    @ApiModelProperty(value = "必发数量")
    @ExcelProperty(value = "必发数量*")
    @ExcelPropertyCheck(required = true)
    private Integer mustQuantity;
    /**
     * 物料器具
     */
    @ApiModelProperty(value = "物料器具")
    @ExcelProperty(value = "物料器具*")
    @ExcelPropertyCheck(required = true)
    private String materialEquipment;
    /**
     * 箱数
     */
    @ApiModelProperty(value = "箱数")
    private String boxNumber;
    /**
     * 发货时间
     */
    @ApiModelProperty(value = "发货时间")
    private Date deliveryTime;
    /**
     * 毛重
     */
    @ApiModelProperty(value = "毛重")
    @ExcelProperty(value = "毛重")
    private BigDecimal grossWeight;
    /**
     * 体积
     */
    @ApiModelProperty(value = "体积")
    @ExcelProperty(value = "体积")
    private BigDecimal volume;
    /**
     * 柜型
     */
    @ApiModelProperty(value = "柜型")
    @ExcelProperty(value = "柜型")
    private String cabinetType;
    /**
     * 提单号
     */
    @ApiModelProperty(value = "提单号")
    @ExcelProperty(value = "提单号")
    private String billLadingNumber;
    /**
     * 发票号
     */
    @ApiModelProperty(value = "发票号")
    @ExcelProperty(value = "发票号")
    private String invoiceNumber;
    /**
     * 实际发货数量
     */
    @ApiModelProperty(value = "实际发货数量")
    private Integer actualDeliveryQuantity;
    /**
     * 实际发货箱数
     */
    @ApiModelProperty(value = "实际发货箱数")
    private Integer actualDeliveryBoxQuantity;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String versionCode;
    /**
     * 需求来源id
     */
    @ApiModelProperty(value = "需求来源id")
    private String demandSourcesId;
    /**
     * 数据源
     */
    @ApiModelProperty(value = "数据源")
    private String dataSources;
    /**
     * 供应类型
     */
    @ApiModelProperty(value = "供应类型")
    private String businessType;
    /**
     * 贸易类型
     */
    @ApiModelProperty(value = "贸易类型")
    private String marketType;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer versionValue;
    
    /**
     * 是否需要冲销
     */
    @ApiModelProperty(value = "是否需要冲销")
    private String writtenOffFlag;
    
    
}
