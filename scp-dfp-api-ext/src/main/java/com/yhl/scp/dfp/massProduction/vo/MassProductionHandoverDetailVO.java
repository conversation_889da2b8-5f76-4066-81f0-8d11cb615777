package com.yhl.scp.dfp.massProduction.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MassProductionHandoverDetailVO</code>
 * <p>
 * 量产移交信息详情表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 16:19:48
 */
@ApiModel(value = "量产移交信息详情表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MassProductionHandoverDetailVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -97082678264523274L;

    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id")
    @FieldInterpretation(value = "主表id")
    private String massProductionHandoverId;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productCode;
    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    @FieldInterpretation(value = "物料名称")
    private String productName;
    /**
     * 零件号
     */
    @ApiModelProperty(value = "零件号")
    @FieldInterpretation(value = "零件号")
    private String partNumber;
    /**
     * 装车位置小类
     */
    @ApiModelProperty(value = "装车位置小类")
    @FieldInterpretation(value = "装车位置小类")
    private String loadingPositionSub;
    /**
     * 包装方式
     */
    @ApiModelProperty(value = "包装方式")
    @FieldInterpretation(value = "包装方式")
    private String boxType;
    /**
     * 单片玻璃重量
     */
    @ApiModelProperty(value = "单片玻璃重量")
    @FieldInterpretation(value = "单片玻璃重量")
    private BigDecimal pieceWeight;
    /**
     * 料箱立项重量
     */
    @ApiModelProperty(value = "料箱立项重量")
    @FieldInterpretation(value = "料箱立项重量")
    private BigDecimal boxWeight;
    /**
     * 包装箱规格(成箱片数)
     */
    @ApiModelProperty(value = "包装箱规格(成箱片数)")
    @FieldInterpretation(value = "包装箱规格(成箱片数)")
    private Integer boxSpec;
    /**
     * 长
     */
    @ApiModelProperty(value = "长")
    @FieldInterpretation(value = "长")
    private BigDecimal productLength;
    /**
     * 宽
     */
    @ApiModelProperty(value = "宽")
    @FieldInterpretation(value = "宽")
    private BigDecimal productWidth;
    /**
     * 厚
     */
    @ApiModelProperty(value = "厚")
    @FieldInterpretation(value = "厚")
    private BigDecimal productThickness;
    /**
     * SOP
     */
    @ApiModelProperty(value = "SOP")
    @FieldInterpretation(value = "SOP")
    private Date productSop;
    /**
     * EOP
     */
    @ApiModelProperty(value = "EOP")
    @FieldInterpretation(value = "EOP")
    private Date productEop;
    /**
     * 零件履历
     */
    @ApiModelProperty(value = "零件履历")
    @FieldInterpretation(value = "零件履历")
    private String partRecord;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;
    
    /**
     * 是否修改人员权限
     */
    @ApiModelProperty(value = "是否修改人员权限")
    @FieldInterpretation(value = "是否修改人员权限")
    private String updateOrderPlannerFlag;
    
    /**
     * 开发类型
     */
    @ApiModelProperty(value = "开发类型")
    @FieldInterpretation(value = "开发类型")
    private String produceType;

    @Override
    public void clean() {

    }

}
