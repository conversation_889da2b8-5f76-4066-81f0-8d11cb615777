package com.yhl.scp.dfp.deliverydockingorder.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>DeliveryDockingOrderDetailVO</code>
 * <p>
 * 发货对接单详情VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-29 13:58:02
 */
@ApiModel(value = "发货对接单详情VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryDockingOrderDetailVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -79344077108059161L;

    /**
     * 发货对接单号
     */
    @ApiModelProperty(value = "发货对接单号")
    @FieldInterpretation(value = "发货对接单号")
    private String deliveryDockingNumber;
    /**
     * 发货对接单行号
     */
    @ApiModelProperty(value = "发货对接单行号")
    @FieldInterpretation(value = "发货对接单行号")
    private String deliveryDockingLineNumber;
    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id")
    @FieldInterpretation(value = "主表id")
    private String deliveryDockingId;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productCode;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;
    /**
     * 主机厂名称
     */
    @ApiModelProperty(value = "主机厂名称")
    @FieldInterpretation(value = "主机厂名称")
    private String oemName;
    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    @FieldInterpretation(value = "客户编码")
    private String customerCode;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    @FieldInterpretation(value = "客户名称")
    private String customerName;

    /**
     * 发货数量
     */
    @ApiModelProperty(value = "发货数量")
    @FieldInterpretation(value = "发货数量")
    private Integer deliveryQuantity;
    /**
     * 必发数量
     */
    @ApiModelProperty(value = "必发数量")
    @FieldInterpretation(value = "必发数量")
    private Integer mustQuantity;
    /**
     * 物料器具
     */
    @ApiModelProperty(value = "物料器具")
    @FieldInterpretation(value = "物料器具")
    private String materialEquipment;
    /**
     * 箱数
     */
    @ApiModelProperty(value = "箱数")
    @FieldInterpretation(value = "箱数")
    private String boxNumber;
    /**
     * 发货时间
     */
    @ApiModelProperty(value = "发货时间")
    @FieldInterpretation(value = "发货时间")
    private Date deliveryTime;
    /**
     * 毛重
     */
    @ApiModelProperty(value = "毛重")
    @FieldInterpretation(value = "毛重")
    private BigDecimal grossWeight;
    /**
     * 体积
     */
    @ApiModelProperty(value = "体积")
    @FieldInterpretation(value = "体积")
    private BigDecimal volume;
    /**
     * 柜型
     */
    @ApiModelProperty(value = "柜型")
    @FieldInterpretation(value = "柜型")
    private String cabinetType;
    /**
     * 提单号
     */
    @ApiModelProperty(value = "提单号")
    @FieldInterpretation(value = "提单号")
    private String billLadingNumber;
    /**
     * 发票号
     */
    @ApiModelProperty(value = "发票号")
    @FieldInterpretation(value = "发票号")
    private String invoiceNumber;
    /**
     * 实际发货数量
     */
    @ApiModelProperty(value = "实际发货数量")
    @FieldInterpretation(value = "实际发货数量")
    private Integer actualDeliveryQuantity;
    /**
     * 实际发货箱数
     */
    @ApiModelProperty(value = "实际发货箱数")
    @FieldInterpretation(value = "实际发货箱数")
    private Integer actualDeliveryBoxQuantity;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @FieldInterpretation(value = "状态")
    private String status;
    /**
     * 运输方式
     */
    @ApiModelProperty(value = "运输方式")
    @FieldInterpretation(value = "运输方式")
    private String transportMode;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String versionCode;
    /**
     * 需求来源id
     */
    @ApiModelProperty(value = "需求来源id")
    private String demandSourcesId;
    /**
     * 数据源
     */
    @ApiModelProperty(value = "数据源")
    private String dataSources;
    /**
     * 供应类型
     */
    @ApiModelProperty(value = "供应类型")
    private String businessType;
    /**
     * 贸易类型
     */
    @ApiModelProperty(value = "贸易类型")
    private String marketType;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
    
    /**
     * 是否需要冲销
     */
    @ApiModelProperty(value = "是否需要冲销")
    private String writtenOffFlag;

    @Override
    public void clean() {

    }

}
